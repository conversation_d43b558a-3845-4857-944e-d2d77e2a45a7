#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化的站点测试脚本
直接测试两个指定站点，不依赖复杂的导入
"""

import requests
import json
import csv
import os
import time
from bs4 import BeautifulSoup
from xml.etree import ElementTree as ET
from urllib.parse import urlparse
import random

# 测试站点
TEST_SITES = [
    {"name": "网球用品店", "sitemap": "https://courtsidetennis.com/sitemap.xml"},
    {"name": "太阳能商店", "sitemap": "https://www.solartopstore.com/sitemap.xml"}
]

# 代理配置
PROXIES = [
    {'http': 'socks5://dxgetpjh:nsueipnrildn@************:5727', 'https': 'socks5://dxgetpjh:nsueipnrildn@************:5727'},
    {'http': 'socks5://dxgetpjh:nsueipnrildn@*************:6455', 'https': 'socks5://dxgetpjh:nsueipnrildn@*************:6455'},
    {'http': 'socks5://dxgetpjh:nsueipnrildn@************:6045', 'https': 'socks5://dxgetpjh:nsueipnrildn@************:6045'}
]

USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
]

def get_random_user_agent():
    return random.choice(USER_AGENTS)

def fetch_url(url, use_proxy=False, timeout=15):
    """获取URL内容"""
    headers = {'User-Agent': get_random_user_agent()}
    
    proxies = None
    if use_proxy and PROXIES:
        proxies = random.choice(PROXIES)
        print(f"  使用代理: {proxies['http'].split('@')[1]}")
    
    try:
        response = requests.get(url, headers=headers, proxies=proxies, timeout=timeout)
        if response.status_code == 200:
            return response.text
        else:
            print(f"  HTTP错误: {response.status_code}")
            return None
    except Exception as e:
        print(f"  请求失败: {e}")
        return None

def parse_sitemap(sitemap_url, use_proxy=False):
    """解析sitemap获取产品URL"""
    print(f"解析sitemap: {sitemap_url}")
    
    content = fetch_url(sitemap_url, use_proxy)
    if not content:
        return []
    
    urls = []
    try:
        root = ET.fromstring(content)
        for url_elem in root.findall(".//{http://www.sitemaps.org/schemas/sitemap/0.9}loc"):
            url = url_elem.text
            if url and ('/products/' in url or '/product/' in url):
                urls.append(url)
        
        # 如果没找到，尝试不使用命名空间
        if not urls:
            for url_elem in root.findall(".//loc"):
                url = url_elem.text
                if url and ('/products/' in url or '/product/' in url):
                    urls.append(url)
    except Exception as e:
        print(f"  sitemap解析失败: {e}")
        return []
    
    print(f"  找到 {len(urls)} 个产品URL")
    return urls

def get_product_json(product_url, use_proxy=False):
    """获取产品JSON数据"""
    json_url = product_url.rstrip('/') + '.json'
    content = fetch_url(json_url, use_proxy)
    
    if not content:
        return None
    
    try:
        data = json.loads(content)
        return data.get('product', {})
    except Exception as e:
        print(f"  JSON解析失败: {e}")
        return None

def test_site(site_info, use_proxy=False, max_products=10):
    """测试单个站点"""
    site_name = site_info['name']
    sitemap_url = site_info['sitemap']
    
    mode = "代理模式" if use_proxy else "直连模式"
    print(f"\n=== {mode}测试: {site_name} ===")
    
    # 解析sitemap
    urls = parse_sitemap(sitemap_url, use_proxy)
    if not urls:
        print("  ❌ 未找到产品URL")
        return {'success': 0, 'failed': 1, 'products': []}
    
    # 限制测试数量
    test_urls = urls[:max_products]
    print(f"  测试前 {len(test_urls)} 个产品")
    
    success_count = 0
    failed_count = 0
    products = []
    
    for i, url in enumerate(test_urls, 1):
        print(f"  [{i}/{len(test_urls)}] {url}")
        
        try:
            product = get_product_json(url, use_proxy)
            if product and product.get('title'):
                print(f"    ✅ {product['title'][:50]}...")
                products.append({
                    'title': product.get('title', ''),
                    'handle': product.get('handle', ''),
                    'price': product.get('variants', [{}])[0].get('price', '') if product.get('variants') else '',
                    'url': url
                })
                success_count += 1
            else:
                print(f"    ❌ 无效产品数据")
                failed_count += 1
        except Exception as e:
            print(f"    ❌ 异常: {e}")
            failed_count += 1
        
        # 延迟
        time.sleep(2 if use_proxy else 1)
    
    result = {
        'success': success_count,
        'failed': failed_count,
        'products': products
    }
    
    print(f"  结果: 成功 {success_count}, 失败 {failed_count}")
    return result

def save_results_to_csv(site_name, mode, products):
    """保存结果到CSV"""
    if not products:
        return
    
    os.makedirs('output', exist_ok=True)
    filename = f"output/{site_name}_{mode}_test.csv"
    
    with open(filename, 'w', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=['title', 'handle', 'price', 'url'])
        writer.writeheader()
        writer.writerows(products)
    
    print(f"  结果已保存到: {filename}")

def main():
    """主函数"""
    print("=== Shopify站点测试工具 ===")
    print("测试站点:")
    for site in TEST_SITES:
        print(f"  - {site['name']}: {site['sitemap']}")
    
    print(f"\n代理配置: {len(PROXIES)} 个SOCKS5代理")
    
    all_results = []
    
    for site in TEST_SITES:
        # 测试直连模式
        print(f"\n{'='*60}")
        direct_result = test_site(site, use_proxy=False, max_products=10)
        save_results_to_csv(site['name'], 'direct', direct_result['products'])
        
        # 测试代理模式
        proxy_result = test_site(site, use_proxy=True, max_products=10)
        save_results_to_csv(site['name'], 'proxy', proxy_result['products'])
        
        all_results.append({
            'site': site['name'],
            'direct': direct_result,
            'proxy': proxy_result
        })
    
    # 显示汇总
    print(f"\n{'='*60}")
    print("测试汇总:")
    print(f"{'='*60}")
    
    for result in all_results:
        print(f"\n{result['site']}:")
        print(f"  直连模式: 成功 {result['direct']['success']}, 失败 {result['direct']['failed']}")
        print(f"  代理模式: 成功 {result['proxy']['success']}, 失败 {result['proxy']['failed']}")
    
    print(f"\n测试完成！请检查output目录下的CSV文件。")

if __name__ == "__main__":
    main()
