#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试修正后的产品转换逻辑
"""

import sys
import json
import csv
import os
from datetime import datetime

# 导入主脚本的函数
try:
    from scrawshopify import product_to_csv_rows, clean_description
except ImportError as e:
    print(f"导入主脚本失败: {e}")
    sys.exit(1)

def test_single_variant_product():
    """测试单变体产品（应该是simple类型）"""
    print("=== 测试1: 单变体产品 ===")
    
    product = {
        "id": 12345,
        "title": "测试单变体产品",
        "handle": "test-single-variant",
        "body_html": "<p>这是一个<strong>单变体</strong>产品描述。\n包含换行符。</p>",
        "tags": ["tag1", "tag2"],
        "images": [{"src": "https://example.com/image1.jpg"}],
        "variants": [
            {
                "id": 1,
                "sku": "TEST-001",
                "price": "99.99",
                "compare_at_price": "129.99",
                "inventory_quantity": 10,
                "option1": "Default Title",
                "option2": None,
                "option3": None
            }
        ]
    }
    
    rows = product_to_csv_rows(product)
    
    print(f"生成行数: {len(rows)}")
    print(f"产品类型: {rows[0]['Type']}")
    print(f"描述是否包含换行: {'\\n' in rows[0]['Description']}")
    
    # 检查属性字段
    attr_fields = [k for k in rows[0].keys() if 'Attribute' in k]
    print(f"属性字段: {attr_fields}")
    
    return rows

def test_multi_variant_product():
    """测试多变体产品"""
    print("\n=== 测试2: 多变体产品 ===")
    
    product = {
        "id": 12346,
        "title": "测试多变体产品",
        "handle": "test-multi-variant",
        "body_html": "<p>这是一个<strong>多变体</strong>产品描述。\n\n包含多个换行符。</p>",
        "tags": ["tag1", "tag2"],
        "images": [{"src": "https://example.com/image1.jpg"}],
        "variants": [
            {
                "id": 1,
                "sku": "TEST-RED-S",
                "price": "99.99",
                "compare_at_price": None,
                "inventory_quantity": 5,
                "option1": "Red",
                "option2": "Small",
                "option3": None
            },
            {
                "id": 2,
                "sku": "TEST-RED-M",
                "price": "99.99",
                "compare_at_price": None,
                "inventory_quantity": 8,
                "option1": "Red",
                "option2": "Medium",
                "option3": None
            },
            {
                "id": 3,
                "sku": "TEST-BLUE-S",
                "price": "109.99",
                "compare_at_price": None,
                "inventory_quantity": 3,
                "option1": "Blue",
                "option2": "Small",
                "option3": None
            }
        ]
    }
    
    rows = product_to_csv_rows(product)
    
    print(f"生成行数: {len(rows)}")
    print(f"父产品类型: {rows[0]['Type']}")
    print(f"子产品类型: {[row['Type'] for row in rows[1:]]}")
    
    # 检查父产品属性
    parent = rows[0]
    print(f"父产品属性1值: {parent.get('Attribute 1 value(s)', 'N/A')}")
    print(f"父产品属性2值: {parent.get('Attribute 2 value(s)', 'N/A')}")
    
    # 检查子产品属性
    for i, child in enumerate(rows[1:], 1):
        print(f"子产品{i} - 属性1: {child.get('Attribute 1 value(s)', 'N/A')}, 属性2: {child.get('Attribute 2 value(s)', 'N/A')}")
        
        # 检查空属性是否留空
        attr3_visible = child.get('Attribute 3 visible', 'NOT_SET')
        print(f"子产品{i} - 属性3 visible: {attr3_visible}")
    
    return rows

def test_no_variant_product():
    """测试无变体产品"""
    print("\n=== 测试3: 无变体产品 ===")
    
    product = {
        "id": 12347,
        "title": "测试无变体产品",
        "handle": "test-no-variant",
        "body_html": "<p>这是一个无变体产品。</p>",
        "tags": ["tag1"],
        "images": [{"src": "https://example.com/image1.jpg"}],
        "variants": []
    }
    
    rows = product_to_csv_rows(product)
    
    print(f"生成行数: {len(rows)}")
    print(f"产品类型: {rows[0]['Type']}")
    
    return rows

def save_test_results(test_name, rows):
    """保存测试结果"""
    os.makedirs('output', exist_ok=True)
    
    filename = f"output/test_{test_name}.csv"
    
    if rows:
        fieldnames = rows[0].keys()
        
        with open(filename, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(rows)
        
        print(f"✅ 测试结果已保存: {filename}")

def test_html_cleaning():
    """测试HTML清理功能"""
    print("\n=== 测试4: HTML清理功能 ===")
    
    test_html = """
    <p>这是一个测试段落。</p>
    <h3>标题</h3>
    <ul>
        <li>列表项1</li>
        <li>列表项2</li>
    </ul>
    <script>alert('dangerous');</script>
    <style>body{color:red;}</style>
    
    多个换行符
    
    
    应该被清理
    """
    
    cleaned = clean_description(test_html)
    
    print("原始HTML:")
    print(repr(test_html))
    print("\n清理后HTML:")
    print(repr(cleaned))
    
    print(f"\n包含换行符: {'\\n' in cleaned}")
    print(f"包含script标签: {'<script' in cleaned}")
    print(f"包含style标签: {'<style' in cleaned}")

def main():
    """主函数"""
    print("=== 修正后的产品转换逻辑测试 ===")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试1: 单变体产品
    single_rows = test_single_variant_product()
    save_test_results("single_variant", single_rows)
    
    # 测试2: 多变体产品
    multi_rows = test_multi_variant_product()
    save_test_results("multi_variant", multi_rows)
    
    # 测试3: 无变体产品
    no_variant_rows = test_no_variant_product()
    save_test_results("no_variant", no_variant_rows)
    
    # 测试4: HTML清理
    test_html_cleaning()
    
    print(f"\n{'='*60}")
    print("测试完成！请检查output目录下的CSV文件")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
