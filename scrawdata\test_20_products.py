#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试采集20个产品，验证数据完整性
"""

import requests
import json
import csv
import os
import re
from datetime import datetime

# 测试站点
TEST_SITES = [
    {"name": "网球用品店", "base_url": "https://courtsidetennis.com"},
    {"name": "太阳能商店", "base_url": "https://www.solartopstore.com"}
]

# Webshare代理配置
WEBSHARE_PROXY = {
    "http": "**************************************************/",
    "https": "**************************************************/"
}

def clean_html_description(html):
    """改进的HTML清理函数"""
    if not html:
        return ""
    
    # 移除脚本和样式标签
    html = re.sub(r'<script[^>]*>.*?</script>', '', html, flags=re.DOTALL | re.IGNORECASE)
    html = re.sub(r'<style[^>]*>.*?</style>', '', html, flags=re.DOTALL | re.IGNORECASE)
    
    # 移除危险标签
    dangerous_tags = ['script', 'style', 'link', 'meta', 'iframe', 'object', 'embed']
    for tag in dangerous_tags:
        html = re.sub(f'<{tag}[^>]*>.*?</{tag}>', '', html, flags=re.DOTALL | re.IGNORECASE)
        html = re.sub(f'<{tag}[^>]*/?>', '', html, flags=re.IGNORECASE)
    
    # 保留有用的HTML标签，移除属性
    allowed_tags = ['p', 'br', 'ul', 'ol', 'li', 'strong', 'em', 'b', 'i', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6']
    
    # 移除所有标签属性，只保留标签本身
    html = re.sub(r'<(\w+)[^>]*>', r'<\1>', html)
    
    # 移除不允许的标签
    html = re.sub(r'<(?!/?(?:' + '|'.join(allowed_tags) + r')\b)[^>]+>', '', html, flags=re.IGNORECASE)
    
    # 清理多余的空白
    html = re.sub(r'\s+', ' ', html)
    html = html.strip()
    
    return html

def convert_product_to_woocommerce(product):
    """转换产品为WooCommerce格式，支持变体"""
    title = product.get('title', '')
    handle = product.get('handle', '')
    body_html = product.get('body_html', '')
    product_id = product.get('id', '')
    
    # 清理HTML描述
    description = clean_html_description(body_html)
    short_description = description[:200] + '...' if len(description) > 200 else description
    
    # 获取图片
    images = product.get('images', [])
    image_urls = [img.get('src', '') for img in images[:5]]  # 最多5张图片
    image_csv = ','.join(image_urls)
    
    # 获取标签
    tags = product.get('tags', [])
    if isinstance(tags, list):
        tags_str = ','.join(tags)
    else:
        tags_str = str(tags) if tags else ''
    
    # 生成SKU
    sku_base = handle[:10].upper() if handle else f"PROD{product_id}"
    
    # 获取变体
    variants = product.get('variants', [])
    
    rows = []
    
    if not variants or len(variants) == 1:
        # 简单产品
        price = variants[0].get('price', '') if variants else ''
        compare_price = variants[0].get('compare_at_price', '') if variants else ''
        inventory_qty = variants[0].get('inventory_quantity', 0) if variants else 0
        
        row = {
            'ID': '',
            'Type': 'simple',
            'SKU': sku_base,
            'Name': title,
            'Published': 1,
            'Short description': short_description,
            'Description': description,
            'Categories': tags_str,
            'Images': image_csv,
            'Regular price': price,
            'Sale price': compare_price if compare_price and float(compare_price or 0) < float(price or 0) else '',
            'In stock?': 1 if inventory_qty > 0 else 0,
            'Stock': inventory_qty if inventory_qty > 0 else '',
            'Visibility in catalog': 'visible',
            'Allow customer reviews?': 1,
            'Product type': 'simple'
        }
        rows.append(row)
        
    else:
        # 变体产品 - 父产品
        parent_row = {
            'ID': '',
            'Type': 'variable',
            'SKU': sku_base,
            'Name': title,
            'Published': 1,
            'Short description': short_description,
            'Description': description,
            'Categories': tags_str,
            'Images': image_csv,
            'In stock?': 1,
            'Stock': '',
            'Visibility in catalog': 'visible',
            'Allow customer reviews?': 1,
            'Product type': 'variable'
        }
        rows.append(parent_row)
        
        # 变体产品 - 子产品
        for i, variant in enumerate(variants):
            variant_sku = variant.get('sku') or f"{sku_base}-V{i+1}"
            price = variant.get('price', '')
            compare_price = variant.get('compare_at_price', '')
            inventory_qty = variant.get('inventory_quantity', 0)
            
            # 获取变体选项
            option1 = variant.get('option1', '')
            option2 = variant.get('option2', '')
            option3 = variant.get('option3', '')
            
            variant_row = {
                'ID': '',
                'Type': 'variation',
                'SKU': variant_sku,
                'Name': title,
                'Published': 1,
                'Short description': short_description,
                'Description': description,
                'Categories': tags_str,
                'Images': image_csv,
                'Regular price': price,
                'Sale price': compare_price if compare_price and float(compare_price or 0) < float(price or 0) else '',
                'In stock?': 1 if inventory_qty > 0 else 0,
                'Stock': inventory_qty if inventory_qty > 0 else '',
                'Parent': sku_base,
                'Product type': 'variation',
                'Attribute 1 name': 'Option 1' if option1 else '',
                'Attribute 1 value(s)': option1,
                'Attribute 1 visible': 1 if option1 else 0,
                'Attribute 1 global': 1 if option1 else 0,
                'Attribute 2 name': 'Option 2' if option2 else '',
                'Attribute 2 value(s)': option2,
                'Attribute 2 visible': 1 if option2 else 0,
                'Attribute 2 global': 1 if option2 else 0,
                'Attribute 3 name': 'Option 3' if option3 else '',
                'Attribute 3 value(s)': option3,
                'Attribute 3 visible': 1 if option3 else 0,
                'Attribute 3 global': 1 if option3 else 0,
            }
            rows.append(variant_row)
    
    return rows

def test_site_detailed(site_info, use_proxy=True, max_products=20):
    """详细测试单个站点"""
    site_name = site_info['name']
    base_url = site_info['base_url']
    
    print(f"\n=== 测试 {site_name} ({'代理' if use_proxy else '直连'}) ===")
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    proxies = WEBSHARE_PROXY if use_proxy else None
    
    # 1. 获取集合
    collections_url = f"{base_url}/collections.json"
    
    try:
        response = requests.get(collections_url, headers=headers, proxies=proxies, timeout=15)
        
        if response.status_code != 200:
            print(f"   ❌ Collections API失败: {response.status_code}")
            return []
        
        collections = response.json().get('collections', [])
        print(f"   找到 {len(collections)} 个集合")
        
    except Exception as e:
        print(f"   ❌ Collections API异常: {e}")
        return []
    
    # 2. 从集合获取产品
    all_products = []
    
    for collection in collections[:3]:  # 测试前3个集合
        collection_handle = collection.get('handle', '')
        collection_title = collection.get('title', collection_handle)
        
        print(f"   采集集合: {collection_title}")
        
        products_url = f"{base_url}/collections/{collection_handle}/products.json"
        
        try:
            response = requests.get(products_url, headers=headers, proxies=proxies, timeout=15)
            
            if response.status_code == 200:
                products = response.json().get('products', [])
                all_products.extend(products)
                print(f"     获得 {len(products)} 个产品")
                
                if len(all_products) >= max_products:
                    all_products = all_products[:max_products]
                    break
            else:
                print(f"     HTTP {response.status_code}")
                
        except Exception as e:
            print(f"     异常: {e}")
    
    print(f"   总计获得 {len(all_products)} 个产品")
    
    # 3. 转换为WooCommerce格式
    all_csv_rows = []
    simple_count = 0
    variable_count = 0
    variation_count = 0
    
    for product in all_products:
        try:
            rows = convert_product_to_woocommerce(product)
            all_csv_rows.extend(rows)
            
            # 统计产品类型
            for row in rows:
                if row['Type'] == 'simple':
                    simple_count += 1
                elif row['Type'] == 'variable':
                    variable_count += 1
                elif row['Type'] == 'variation':
                    variation_count += 1
                    
        except Exception as e:
            print(f"     产品转换失败: {e}")
    
    print(f"   转换结果: {len(all_csv_rows)} 行CSV数据")
    print(f"   产品类型: 简单 {simple_count}, 变体父 {variable_count}, 变体子 {variation_count}")
    
    # 4. 保存结果
    mode = 'proxy' if use_proxy else 'direct'
    save_detailed_results(site_name, mode, all_csv_rows, all_products)
    
    return all_csv_rows

def save_detailed_results(site_name, mode, csv_rows, raw_products):
    """保存详细测试结果"""
    os.makedirs('output', exist_ok=True)
    
    # 保存CSV
    if csv_rows:
        csv_filename = f"output/{site_name}_{mode}_detailed.csv"
        
        fieldnames = csv_rows[0].keys() if csv_rows else []
        
        with open(csv_filename, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(csv_rows)
        
        print(f"   ✅ CSV保存: {csv_filename}")
    
    # 保存原始数据
    if raw_products:
        json_filename = f"output/{site_name}_{mode}_raw.json"
        
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(raw_products, f, indent=2, ensure_ascii=False)
        
        print(f"   ✅ 原始数据保存: {json_filename}")

def analyze_csv_quality(csv_file):
    """分析CSV数据质量"""
    if not os.path.exists(csv_file):
        return
    
    print(f"\n--- 数据质量分析: {os.path.basename(csv_file)} ---")
    
    with open(csv_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        rows = list(reader)
    
    print(f"总行数: {len(rows)}")
    
    # 产品类型统计
    types = {}
    for row in rows:
        product_type = row.get('Type', 'unknown')
        types[product_type] = types.get(product_type, 0) + 1
    
    print(f"产品类型: {types}")
    
    # 数据完整性检查
    complete_data = {
        'name': sum(1 for row in rows if row.get('Name')),
        'sku': sum(1 for row in rows if row.get('SKU')),
        'price': sum(1 for row in rows if row.get('Regular price')),
        'description': sum(1 for row in rows if row.get('Description')),
        'images': sum(1 for row in rows if row.get('Images')),
    }
    
    print("数据完整性:")
    for field, count in complete_data.items():
        percentage = count / len(rows) * 100
        print(f"  {field}: {count}/{len(rows)} ({percentage:.1f}%)")
    
    # HTML标签分析
    html_tags = set()
    clean_descriptions = 0
    
    for row in rows:
        desc = row.get('Description', '')
        if desc:
            # 查找HTML标签
            tags = re.findall(r'<(\w+)', desc)
            html_tags.update(tags)
            
            # 检查是否包含危险标签
            if not re.search(r'<(?:script|style|link|meta|iframe)', desc, re.IGNORECASE):
                clean_descriptions += 1
    
    total_with_desc = sum(1 for row in rows if row.get('Description'))
    clean_percentage = clean_descriptions / max(1, total_with_desc) * 100
    
    print(f"HTML处理:")
    print(f"  有描述的产品: {total_with_desc}")
    print(f"  清洁描述: {clean_descriptions} ({clean_percentage:.1f}%)")
    print(f"  发现的HTML标签: {sorted(list(html_tags))}")

def main():
    """主函数"""
    print("=== 20产品详细测试 ===")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    all_results = []
    
    for site in TEST_SITES:
        # 测试直连
        print(f"\n{'='*60}")
        direct_rows = test_site_detailed(site, use_proxy=False, max_products=20)
        all_results.append(('direct', site['name'], len(direct_rows)))
        
        # 测试代理
        proxy_rows = test_site_detailed(site, use_proxy=True, max_products=20)
        all_results.append(('proxy', site['name'], len(proxy_rows)))
    
    # 汇总结果
    print(f"\n{'='*60}")
    print("测试汇总:")
    print(f"{'='*60}")
    
    for mode, site, count in all_results:
        status = "✅" if count > 0 else "❌"
        print(f"{status} {site} ({mode}): {count} 行CSV数据")
    
    # 分析数据质量
    print(f"\n{'='*60}")
    print("数据质量分析:")
    print(f"{'='*60}")
    
    csv_files = [
        'output/网球用品店_direct_detailed.csv',
        'output/网球用品店_proxy_detailed.csv',
        'output/太阳能商店_direct_detailed.csv',
        'output/太阳能商店_proxy_detailed.csv'
    ]
    
    for csv_file in csv_files:
        analyze_csv_quality(csv_file)
    
    print(f"\n{'='*60}")
    print("测试完成！请检查output目录下的文件")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
