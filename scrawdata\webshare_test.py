#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Webshare代理测试脚本
测试Webshare代理和两个指定站点的采集
"""

import requests
import json
import csv
import os
import time
from xml.etree import ElementTree as ET
from datetime import datetime

# Webshare代理配置
WEBSHARE_PROXY = {
    "http": "http://dxgetpjh-US:nsueipnrildn@*************:6212/",
    "https": "http://dxgetpjh-US:nsueipnrildn@*************:6212/"
}

# 测试站点
TEST_SITES = [
    {"name": "courtsidetennis", "sitemap": "https://courtsidetennis.com/sitemap.xml"},
    {"name": "solartopstore", "sitemap": "https://www.solartopstore.com/sitemap.xml"}
]

def test_webshare_connection():
    """测试Webshare代理连接"""
    print("=== 测试Webshare代理连接 ===")
    
    try:
        # 测试代理IP
        response = requests.get(
            "https://ipv4.webshare.io/",
            proxies=WEBSHARE_PROXY,
            timeout=15
        )
        
        if response.status_code == 200:
            print(f"✅ Webshare代理连接成功")
            print(f"   响应内容: {response.text[:100]}...")
            return True
        else:
            print(f"❌ Webshare代理连接失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Webshare代理连接异常: {e}")
        return False

def test_site_with_webshare(site_info, max_products=10):
    """使用Webshare代理测试站点采集"""
    site_name = site_info['name']
    sitemap_url = site_info['sitemap']
    
    print(f"\n=== 使用Webshare代理测试: {site_name} ===")
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    }
    
    # 1. 测试sitemap访问
    print("1. 测试sitemap访问...")
    try:
        response = requests.get(
            sitemap_url,
            headers=headers,
            proxies=WEBSHARE_PROXY,
            timeout=20
        )
        
        if response.status_code != 200:
            print(f"   ❌ Sitemap访问失败: HTTP {response.status_code}")
            return {'success': 0, 'failed': 1, 'products': []}
        
        print(f"   ✅ Sitemap访问成功，内容长度: {len(response.text)}")
        
    except Exception as e:
        print(f"   ❌ Sitemap访问异常: {e}")
        return {'success': 0, 'failed': 1, 'products': []}
    
    # 2. 解析产品URL
    print("2. 解析产品URL...")
    try:
        root = ET.fromstring(response.text)
        product_urls = []
        
        # 尝试标准命名空间
        for url_elem in root.findall(".//{http://www.sitemaps.org/schemas/sitemap/0.9}loc"):
            url = url_elem.text
            if url and ('/products/' in url or '/product/' in url):
                product_urls.append(url)
        
        # 如果没找到，尝试不使用命名空间
        if not product_urls:
            for url_elem in root.findall(".//loc"):
                url = url_elem.text
                if url and ('/products/' in url or '/product/' in url):
                    product_urls.append(url)
        
        print(f"   找到 {len(product_urls)} 个产品URL")
        
        if not product_urls:
            print("   ❌ 未找到产品URL")
            return {'success': 0, 'failed': 1, 'products': []}
            
    except Exception as e:
        print(f"   ❌ XML解析失败: {e}")
        return {'success': 0, 'failed': 1, 'products': []}
    
    # 3. 测试产品采集
    print(f"3. 测试产品采集（前{max_products}个）...")
    test_urls = product_urls[:max_products]
    
    success_count = 0
    failed_count = 0
    products = []
    
    for i, product_url in enumerate(test_urls, 1):
        print(f"   [{i}/{len(test_urls)}] {product_url}")
        
        try:
            # 获取产品JSON
            json_url = product_url.rstrip('/') + '.json'
            
            response = requests.get(
                json_url,
                headers=headers,
                proxies=WEBSHARE_PROXY,
                timeout=15
            )
            
            if response.status_code == 200:
                data = response.json()
                product = data.get('product', {})
                
                if product and product.get('title'):
                    title = product.get('title', '')
                    handle = product.get('handle', '')
                    variants = product.get('variants', [])
                    price = variants[0].get('price', '') if variants else ''
                    
                    products.append({
                        'title': title,
                        'handle': handle,
                        'price': price,
                        'url': product_url,
                        'variants_count': len(variants)
                    })
                    
                    print(f"      ✅ {title[:40]}... (价格: {price})")
                    success_count += 1
                else:
                    print(f"      ❌ 产品数据为空")
                    failed_count += 1
            else:
                print(f"      ❌ HTTP {response.status_code}")
                failed_count += 1
                
        except Exception as e:
            print(f"      ❌ 异常: {e}")
            failed_count += 1
        
        # 延迟避免请求过快
        time.sleep(2)
    
    result = {
        'success': success_count,
        'failed': failed_count,
        'products': products
    }
    
    print(f"\n{site_name} 测试结果:")
    print(f"   成功: {success_count}")
    print(f"   失败: {failed_count}")
    
    return result

def save_results_to_csv(site_name, products):
    """保存结果到CSV"""
    if not products:
        return
    
    os.makedirs('output', exist_ok=True)
    filename = f"output/{site_name}_webshare_test.csv"
    
    with open(filename, 'w', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=['title', 'handle', 'price', 'url', 'variants_count'])
        writer.writeheader()
        writer.writerows(products)
    
    print(f"   结果已保存到: {filename}")

def main():
    """主函数"""
    print("=== Webshare代理 + Shopify站点测试 ===")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"代理地址: {WEBSHARE_PROXY['http']}")
    
    # 1. 测试代理连接
    if not test_webshare_connection():
        print("\n❌ 代理连接失败，测试终止")
        return
    
    # 2. 测试站点采集
    all_results = []
    
    for site in TEST_SITES:
        result = test_site_with_webshare(site, max_products=10)
        save_results_to_csv(site['name'], result['products'])
        all_results.append({
            'site': site['name'],
            'result': result
        })
    
    # 3. 显示汇总结果
    print(f"\n{'='*60}")
    print("测试汇总结果")
    print(f"{'='*60}")
    
    total_success = 0
    total_failed = 0
    
    for result in all_results:
        site_name = result['site']
        success = result['result']['success']
        failed = result['result']['failed']
        
        total_success += success
        total_failed += failed
        
        status = "✅" if success > 0 else "❌"
        print(f"{status} {site_name}: 成功 {success}, 失败 {failed}")
    
    print(f"\n总计: 成功 {total_success}, 失败 {total_failed}")
    
    if total_success > 0:
        print("🎉 Webshare代理测试成功！可以用于正式采集")
        print("请检查output目录下的CSV文件查看采集结果")
    else:
        print("❌ 测试失败，请检查代理配置或网络连接")

if __name__ == "__main__":
    main()
