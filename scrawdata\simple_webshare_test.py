#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import time

# Webshare代理配置
WEBSHARE_PROXY = {
    "http": "http://dxgetpjh-US:nsueipnrildn@*************:6212/",
    "https": "http://dxgetpjh-US:nsueipnrildn@*************:6212/"
}

def test_basic():
    print("=== 基本Webshare代理测试 ===")
    
    # 测试1: 检查IP
    print("1. 检查代理IP...")
    try:
        response = requests.get(
            "https://ipv4.webshare.io/",
            proxies=WEBSHARE_PROXY,
            timeout=10
        )
        print(f"   ✅ 代理IP: {response.text.strip()}")
    except Exception as e:
        print(f"   ❌ 代理测试失败: {e}")
        return False
    
    # 测试2: 访问第一个站点
    print("\n2. 测试访问courtsidetennis.com...")
    try:
        response = requests.get(
            "https://courtsidetennis.com/",
            proxies=WEBSHARE_PROXY,
            timeout=15,
            headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
        )
        print(f"   状态码: {response.status_code}")
        print(f"   内容长度: {len(response.text)}")
        if "tennis" in response.text.lower():
            print("   ✅ 网站内容正常")
        else:
            print("   ⚠️ 网站内容可能异常")
    except Exception as e:
        print(f"   ❌ 访问失败: {e}")
    
    # 测试3: 访问第二个站点
    print("\n3. 测试访问solartopstore.com...")
    try:
        response = requests.get(
            "https://www.solartopstore.com/",
            proxies=WEBSHARE_PROXY,
            timeout=15,
            headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
        )
        print(f"   状态码: {response.status_code}")
        print(f"   内容长度: {len(response.text)}")
        if "solar" in response.text.lower():
            print("   ✅ 网站内容正常")
        else:
            print("   ⚠️ 网站内容可能异常")
    except Exception as e:
        print(f"   ❌ 访问失败: {e}")
    
    # 测试4: Collections API
    print("\n4. 测试Collections API...")
    try:
        response = requests.get(
            "https://courtsidetennis.com/collections.json",
            proxies=WEBSHARE_PROXY,
            timeout=15,
            headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
        )
        print(f"   courtsidetennis collections: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            collections = data.get('collections', [])
            print(f"   找到 {len(collections)} 个集合")
            if collections:
                print(f"   第一个集合: {collections[0].get('title', 'N/A')}")
    except Exception as e:
        print(f"   ❌ Collections API失败: {e}")
    
    print("\n测试完成!")
    return True

if __name__ == "__main__":
    test_basic()
