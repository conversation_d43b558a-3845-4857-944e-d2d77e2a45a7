#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import time
import json
from xml.etree import ElementTree as ET

# SOCKS5代理配置
PROXIES = [
    {
        'http': 'socks5://dxgetpjh:nsueipnrildn@************:5727',
        'https': 'socks5://dxgetpjh:nsueipnrildn@************:5727'
    },
    {
        'http': 'socks5://dxgetpjh:nsueipnrildn@*************:6455', 
        'https': 'socks5://dxgetpjh:nsueipnrildn@*************:6455'
    },
    {
        'http': 'socks5://dxgetpjh:nsueipnrildn@************:6045',
        'https': 'socks5://dxgetpjh:nsueipnrildn@************:6045'
    }
]

def test_proxy_connection():
    """测试代理连接"""
    print("=== 代理连接测试 ===")
    
    for i, proxy in enumerate(PROXIES, 1):
        print(f"\n测试代理 {i}: {proxy['http'].split('@')[1]}")
        
        try:
            response = requests.get(
                "http://httpbin.org/ip", 
                proxies=proxy, 
                timeout=15
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"  ✅ 连接成功，IP: {data['origin']}")
                return proxy  # 返回第一个可用的代理
            else:
                print(f"  ❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ 连接失败: {e}")
    
    print("❌ 所有代理都无法连接")
    return None

def test_sitemap_with_proxy(proxy):
    """使用代理测试sitemap"""
    print("\n=== 使用代理测试Sitemap ===")
    
    sitemaps = [
        "https://courtsidetennis.com/sitemap.xml",
        "https://www.solartopstore.com/sitemap.xml"
    ]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    results = []
    
    for sitemap in sitemaps:
        print(f"\n测试: {sitemap}")
        
        try:
            response = requests.get(
                sitemap, 
                headers=headers, 
                proxies=proxy, 
                timeout=20
            )
            
            print(f"  状态码: {response.status_code}")
            
            if response.status_code == 200:
                content = response.text
                print(f"  内容长度: {len(content)}")
                
                # 解析XML查找产品URL
                try:
                    root = ET.fromstring(content)
                    product_urls = []
                    
                    # 尝试标准命名空间
                    for url_elem in root.findall(".//{http://www.sitemaps.org/schemas/sitemap/0.9}loc"):
                        url = url_elem.text
                        if url and ('/products/' in url or '/product/' in url):
                            product_urls.append(url)
                    
                    # 如果没找到，尝试不使用命名空间
                    if not product_urls:
                        for url_elem in root.findall(".//loc"):
                            url = url_elem.text
                            if url and ('/products/' in url or '/product/' in url):
                                product_urls.append(url)
                    
                    print(f"  找到产品URL: {len(product_urls)}")
                    if product_urls:
                        print(f"  示例URL: {product_urls[0]}")
                        
                        # 测试第一个产品的JSON
                        test_product_json(product_urls[0], proxy, headers)
                    
                    results.append({
                        'sitemap': sitemap,
                        'success': True,
                        'product_count': len(product_urls),
                        'sample_url': product_urls[0] if product_urls else None
                    })
                    
                except Exception as e:
                    print(f"  XML解析失败: {e}")
                    results.append({
                        'sitemap': sitemap,
                        'success': False,
                        'error': str(e)
                    })
            else:
                print(f"  ❌ HTTP错误: {response.status_code}")
                results.append({
                    'sitemap': sitemap,
                    'success': False,
                    'error': f'HTTP {response.status_code}'
                })
                
        except Exception as e:
            print(f"  ❌ 请求失败: {e}")
            results.append({
                'sitemap': sitemap,
                'success': False,
                'error': str(e)
            })
        
        time.sleep(2)
    
    return results

def test_product_json(product_url, proxy, headers):
    """测试产品JSON获取"""
    print(f"    测试产品JSON: {product_url}")
    
    json_url = product_url.rstrip('/') + '.json'
    
    try:
        response = requests.get(
            json_url,
            headers=headers,
            proxies=proxy,
            timeout=15
        )
        
        if response.status_code == 200:
            data = response.json()
            product = data.get('product', {})
            
            if product:
                title = product.get('title', 'N/A')
                handle = product.get('handle', 'N/A')
                variants = product.get('variants', [])
                price = variants[0].get('price', 'N/A') if variants else 'N/A'
                
                print(f"    ✅ 产品JSON获取成功")
                print(f"       标题: {title[:50]}...")
                print(f"       价格: {price}")
                return True
            else:
                print(f"    ❌ 产品数据为空")
                return False
        else:
            print(f"    ❌ JSON HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"    ❌ JSON获取失败: {e}")
        return False

def main():
    """主函数"""
    print("=== Shopify站点代理测试 ===")
    
    # 测试代理连接
    working_proxy = test_proxy_connection()
    
    if not working_proxy:
        print("\n❌ 无可用代理，测试终止")
        return
    
    # 使用可用代理测试sitemap
    results = test_sitemap_with_proxy(working_proxy)
    
    # 显示汇总结果
    print(f"\n{'='*50}")
    print("测试汇总:")
    print(f"{'='*50}")
    
    for result in results:
        sitemap = result['sitemap'].split('//')[1].split('/')[0]
        if result['success']:
            print(f"✅ {sitemap}: 找到 {result['product_count']} 个产品")
        else:
            print(f"❌ {sitemap}: {result['error']}")
    
    print("\n测试完成!")

if __name__ == "__main__":
    main()
