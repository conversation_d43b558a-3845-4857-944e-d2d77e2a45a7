#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析Profishop的6个CSV文件，提供优化建议
"""

import pandas as pd
import numpy as np
from collections import Counter
import re
from pathlib import Path

def analyze_profishop_file(file_path, file_name):
    """分析单个Profishop文件"""
    print(f'📊 分析 {file_name}')
    print('='*50)
    
    try:
        # 读取文件基本信息
        df = pd.read_csv(file_path, encoding='utf-8-sig')
        
        print(f'总产品数: {len(df):,}')
        print(f'字段数量: {len(df.columns)}')
        
        # 分析关键字段覆盖率
        key_fields = ['Name', 'Short description', 'Description', 'Categories', 'Tags', 'Images', 'Regular price']
        
        print(f'\n关键字段覆盖率:')
        for field in key_fields:
            if field in df.columns:
                non_empty = df[field].notna().sum()
                coverage = non_empty / len(df) * 100
                print(f'  {field}: {non_empty:,}个 ({coverage:.1f}%)')
        
        # 分析Tags字段
        if 'Tags' in df.columns:
            print(f'\n🏷️ Tags分析:')
            tags_data = df['Tags'].dropna()
            
            if len(tags_data) > 0:
                print(f'  有Tags的产品: {len(tags_data):,}个 ({len(tags_data)/len(df)*100:.1f}%)')
                
                # 分析Tags内容
                all_tags = []
                tag_counts = []
                
                for tags in tags_data.head(1000):  # 分析前1000个
                    tag_str = str(tags)
                    # 分割Tags (可能用逗号、分号等分隔)
                    if ',' in tag_str:
                        individual_tags = [tag.strip() for tag in tag_str.split(',')]
                    elif ';' in tag_str:
                        individual_tags = [tag.strip() for tag in tag_str.split(';')]
                    else:
                        individual_tags = [tag_str.strip()]
                    
                    tag_counts.append(len(individual_tags))
                    all_tags.extend(individual_tags)
                
                # 统计Tags数量分布
                if tag_counts:
                    avg_tags = sum(tag_counts) / len(tag_counts)
                    max_tags = max(tag_counts)
                    print(f'  平均Tags数量: {avg_tags:.1f}个')
                    print(f'  最多Tags数量: {max_tags}个')
                
                # 统计最常见的Tags
                if all_tags:
                    tag_counter = Counter(all_tags)
                    print(f'  最常见的Tags:')
                    for tag, count in tag_counter.most_common(10):
                        if tag and len(tag.strip()) > 1:
                            print(f'    "{tag}" - 出现{count}次')
        
        # 分析产品名称特点
        print(f'\n📦 产品名称分析:')
        names = df['Name'].dropna()
        if len(names) > 0:
            name_lengths = [len(str(name)) for name in names.head(1000)]
            avg_length = sum(name_lengths) / len(name_lengths)
            print(f'  平均长度: {avg_length:.1f} 字符')
            
            # 提取关键词
            all_words = []
            for name in names.head(1000):
                words = str(name).lower().split()
                all_words.extend([word for word in words if len(word) > 2])
            
            word_counter = Counter(all_words)
            print(f'  最常见关键词:')
            for word, count in word_counter.most_common(8):
                print(f'    "{word}" - {count}次')
        
        # 分析分类
        print(f'\n🏷️ 分类分析:')
        categories = df['Categories'].dropna()
        if len(categories) > 0:
            unique_cats = categories.nunique()
            print(f'  唯一分类数: {unique_cats}')
            
            # 显示主要分类
            cat_counts = categories.value_counts().head(5)
            print(f'  主要分类:')
            for cat, count in cat_counts.items():
                print(f'    "{cat}" - {count}个产品')
        
        # 分析价格范围
        print(f'\n💰 价格分析:')
        prices = df['Regular price'].dropna()
        if len(prices) > 0:
            price_values = []
            for price in prices:
                try:
                    price_val = float(price)
                    if price_val > 0:
                        price_values.append(price_val)
                except:
                    pass
            
            if price_values:
                avg_price = sum(price_values) / len(price_values)
                min_price = min(price_values)
                max_price = max(price_values)
                
                print(f'  价格范围: €{min_price:.2f} - €{max_price:.2f}')
                print(f'  平均价格: €{avg_price:.2f}')
        
        return {
            'file_name': file_name,
            'product_count': len(df),
            'tags_coverage': len(df['Tags'].dropna()) / len(df) * 100 if 'Tags' in df.columns else 0,
            'avg_price': avg_price if 'price_values' in locals() and price_values else 0,
            'categories_count': categories.nunique() if len(categories) > 0 else 0
        }
        
    except Exception as e:
        print(f'❌ 分析失败: {e}')
        return None

def analyze_tags_patterns(file_paths):
    """分析所有文件的Tags模式"""
    print(f'\n🔍 跨文件Tags模式分析')
    print('='*60)
    
    all_tags = []
    tag_patterns = {}
    
    for file_path in file_paths:
        try:
            df = pd.read_csv(file_path, encoding='utf-8-sig', nrows=500)  # 每个文件分析500行
            
            if 'Tags' in df.columns:
                tags_data = df['Tags'].dropna()
                
                for tags in tags_data:
                    tag_str = str(tags)
                    
                    # 识别分隔符模式
                    if ',' in tag_str:
                        separator = ','
                        individual_tags = [tag.strip() for tag in tag_str.split(',')]
                    elif ';' in tag_str:
                        separator = ';'
                        individual_tags = [tag.strip() for tag in tag_str.split(';')]
                    elif '|' in tag_str:
                        separator = '|'
                        individual_tags = [tag.strip() for tag in tag_str.split('|')]
                    else:
                        separator = 'single'
                        individual_tags = [tag_str.strip()]
                    
                    # 统计分隔符使用
                    tag_patterns[separator] = tag_patterns.get(separator, 0) + 1
                    
                    # 收集所有tags
                    all_tags.extend([tag for tag in individual_tags if tag and len(tag.strip()) > 1])
                    
        except Exception as e:
            print(f'处理文件失败: {Path(file_path).name} - {e}')
    
    # 分析结果
    print(f'Tags分隔符使用统计:')
    for separator, count in sorted(tag_patterns.items(), key=lambda x: x[1], reverse=True):
        print(f'  {separator}: {count}次使用')
    
    # 分析最常见的Tags
    if all_tags:
        tag_counter = Counter(all_tags)
        print(f'\n最常见的Tags (跨所有文件):')
        for tag, count in tag_counter.most_common(20):
            print(f'  "{tag}" - 出现{count}次')
        
        return tag_counter
    
    return None

def generate_profishop_optimization_strategy(file_stats, common_tags):
    """生成Profishop优化策略"""
    print(f'\n💡 Profishop优化策略')
    print('='*60)
    
    # 分析产品类型
    print('1. 产品类型识别:')
    print('   基于文件名和内容分析，Profishop主要销售:')
    print('   • 专业工具和设备')
    print('   • 工业用品和配件') 
    print('   • 建筑和维修工具')
    print('   • 安全防护用品')
    
    # Tags优化策略
    print(f'\n2. Tags优化策略:')
    print('   当前问题:')
    print('   • Tags数量过多，影响SEO效果')
    print('   • 包含过多通用词汇')
    print('   • 缺乏产品核心特征标签')
    
    print(f'\n   优化方案:')
    print('   • 保留1-2个最核心的产品特征Tags')
    print('   • 优先保留品牌名称和产品类型')
    print('   • 移除通用营销词汇')
    print('   • 统一使用逗号分隔符')
    
    # 核心Tags分类
    if common_tags:
        print(f'\n3. 核心Tags分类建议:')
        
        # 品牌Tags
        brand_tags = [tag for tag, count in common_tags.most_common(50) 
                     if any(brand in tag.lower() for brand in ['bosch', 'makita', 'dewalt', 'milwaukee', 'festool'])]
        if brand_tags:
            print(f'   品牌Tags (优先保留): {brand_tags[:5]}')
        
        # 产品类型Tags  
        product_tags = [tag for tag, count in common_tags.most_common(50)
                       if any(ptype in tag.lower() for ptype in ['werkzeug', 'bohrer', 'säge', 'schrauber', 'hammer'])]
        if product_tags:
            print(f'   产品类型Tags (优先保留): {product_tags[:5]}')
        
        # 需要移除的通用Tags
        generic_tags = [tag for tag, count in common_tags.most_common(20)
                       if any(generic in tag.lower() for generic in ['günstig', 'schnell', 'qualität', 'service'])]
        if generic_tags:
            print(f'   通用Tags (建议移除): {generic_tags[:5]}')
    
    print(f'\n4. 短描述优化建议:')
    print('   • 突出专业工具的技术规格')
    print('   • 强调耐用性和可靠性')
    print('   • 提及适用的工作场景')
    print('   • 包含安全认证信息')
    
    return {
        'tags_strategy': 'keep_core_1_2_tags',
        'focus_areas': ['brand', 'product_type', 'technical_specs'],
        'remove_generic': True
    }

def main():
    """主函数"""
    print('🔍 Profishop 6个CSV文件深度分析')
    print('='*60)
    
    # 获取所有Profishop文件
    profishop_files = [
        'woocommerce_output_final/woocommerce_profishop-de_final.csv',
        'woocommerce_output_final/woocommerce_profishop-de-2_final.csv', 
        'woocommerce_output_final/woocommerce_profishop-de-3_final.csv',
        'woocommerce_output_final/woocommerce_profishop-de-4_final.csv',
        'woocommerce_output_final/woocommerce_profishop-de-5_final.csv',
        'woocommerce_output_final/woocommerce_profishop-de-6_final.csv'
    ]
    
    file_stats = []
    
    # 分析每个文件
    for i, file_path in enumerate(profishop_files, 1):
        if Path(file_path).exists():
            file_name = f'Profishop-{i}'
            stats = analyze_profishop_file(file_path, file_name)
            if stats:
                file_stats.append(stats)
            print('\n' + '-'*60)
        else:
            print(f'❌ 文件不存在: {file_path}')
    
    # 跨文件Tags分析
    existing_files = [f for f in profishop_files if Path(f).exists()]
    if existing_files:
        common_tags = analyze_tags_patterns(existing_files)
        
        # 生成优化策略
        optimization_strategy = generate_profishop_optimization_strategy(file_stats, common_tags)
        
        # 总结统计
        print(f'\n📊 总体统计')
        print('='*60)
        total_products = sum(stat['product_count'] for stat in file_stats)
        avg_tags_coverage = sum(stat['tags_coverage'] for stat in file_stats) / len(file_stats)
        
        print(f'总产品数: {total_products:,}')
        print(f'平均Tags覆盖率: {avg_tags_coverage:.1f}%')
        print(f'文件数量: {len(file_stats)}')
        
        for stat in file_stats:
            print(f'  {stat["file_name"]}: {stat["product_count"]:,}个产品')

if __name__ == "__main__":
    main()
