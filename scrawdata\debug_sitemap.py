#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Sitemap调试脚本
查看sitemap的实际内容和结构
"""

import requests
import json
from xml.etree import ElementTree as ET

# Webshare代理配置
WEBSHARE_PROXY = {
    "http": "**************************************************/",
    "https": "**************************************************/"
}

# 测试站点
TEST_SITES = [
    {"name": "courtsidetennis", "sitemap": "https://courtsidetennis.com/sitemap.xml"},
    {"name": "solartopstore", "sitemap": "https://www.solartopstore.com/sitemap.xml"}
]

def debug_sitemap(site_info):
    """调试sitemap内容"""
    site_name = site_info['name']
    sitemap_url = site_info['sitemap']
    
    print(f"\n=== 调试 {site_name} 的sitemap ===")
    print(f"URL: {sitemap_url}")
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    try:
        response = requests.get(
            sitemap_url,
            headers=headers,
            proxies=WEBSHARE_PROXY,
            timeout=20
        )
        
        print(f"状态码: {response.status_code}")
        print(f"内容长度: {len(response.text)}")
        
        if response.status_code == 200:
            content = response.text
            print(f"\n原始内容预览:")
            print("-" * 50)
            print(content[:500])
            print("-" * 50)
            
            # 尝试解析XML
            try:
                root = ET.fromstring(content)
                print(f"\nXML根元素: {root.tag}")
                print(f"命名空间: {root.attrib}")
                
                # 查找所有URL
                all_urls = []
                
                # 方法1: 使用标准命名空间
                for url_elem in root.findall(".//{http://www.sitemaps.org/schemas/sitemap/0.9}loc"):
                    all_urls.append(url_elem.text)
                
                # 方法2: 不使用命名空间
                if not all_urls:
                    for url_elem in root.findall(".//loc"):
                        all_urls.append(url_elem.text)
                
                print(f"\n找到的所有URL ({len(all_urls)}个):")
                for i, url in enumerate(all_urls[:10], 1):  # 只显示前10个
                    print(f"  {i}. {url}")
                
                if len(all_urls) > 10:
                    print(f"  ... 还有 {len(all_urls) - 10} 个URL")
                
                # 查找产品URL
                product_urls = []
                for url in all_urls:
                    if url and ('/products/' in url or '/product/' in url):
                        product_urls.append(url)
                
                print(f"\n产品URL ({len(product_urls)}个):")
                for i, url in enumerate(product_urls[:5], 1):  # 只显示前5个
                    print(f"  {i}. {url}")
                
                # 如果没有产品URL，尝试其他可能的路径
                if not product_urls:
                    print("\n未找到标准产品URL，查找其他可能的产品路径:")
                    possible_patterns = ['/shop/', '/item/', '/p/', '/catalog/']
                    
                    for pattern in possible_patterns:
                        matching_urls = [url for url in all_urls if pattern in url]
                        if matching_urls:
                            print(f"  包含 '{pattern}' 的URL ({len(matching_urls)}个):")
                            for url in matching_urls[:3]:
                                print(f"    - {url}")
                
                # 检查是否是sitemap索引
                if 'sitemapindex' in root.tag.lower():
                    print("\n这是一个sitemap索引文件，包含子sitemap:")
                    for sitemap_elem in root.findall(".//{http://www.sitemaps.org/schemas/sitemap/0.9}sitemap"):
                        loc_elem = sitemap_elem.find("{http://www.sitemaps.org/schemas/sitemap/0.9}loc")
                        if loc_elem is not None:
                            print(f"  - {loc_elem.text}")
                
            except ET.ParseError as e:
                print(f"\nXML解析失败: {e}")
                print("可能不是标准的XML sitemap格式")
                
        else:
            print(f"HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"请求失败: {e}")

def test_collections_api(site_info):
    """测试Collections API"""
    site_name = site_info['name']
    base_url = site_info['sitemap'].replace('/sitemap.xml', '')
    
    print(f"\n=== 测试 {site_name} 的Collections API ===")
    
    collections_url = f"{base_url}/collections.json"
    print(f"Collections URL: {collections_url}")
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    try:
        response = requests.get(
            collections_url,
            headers=headers,
            proxies=WEBSHARE_PROXY,
            timeout=15
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                collections = data.get('collections', [])
                print(f"找到 {len(collections)} 个集合:")
                
                for i, collection in enumerate(collections[:5], 1):
                    handle = collection.get('handle', 'N/A')
                    title = collection.get('title', 'N/A')
                    print(f"  {i}. {handle} - {title}")
                
                # 测试第一个集合的产品
                if collections:
                    first_collection = collections[0]['handle']
                    products_url = f"{base_url}/collections/{first_collection}/products.json"
                    print(f"\n测试集合产品: {products_url}")
                    
                    response = requests.get(
                        products_url,
                        headers=headers,
                        proxies=WEBSHARE_PROXY,
                        timeout=15
                    )
                    
                    if response.status_code == 200:
                        products_data = response.json()
                        products = products_data.get('products', [])
                        print(f"集合 '{first_collection}' 包含 {len(products)} 个产品")
                        
                        if products:
                            product = products[0]
                            print(f"示例产品: {product.get('title', 'N/A')}")
                            print(f"产品ID: {product.get('id', 'N/A')}")
                            print(f"产品Handle: {product.get('handle', 'N/A')}")
                    else:
                        print(f"集合产品请求失败: HTTP {response.status_code}")
                        
            except json.JSONDecodeError:
                print("响应不是有效的JSON格式")
        else:
            print(f"Collections API不可用: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"Collections API测试失败: {e}")

def main():
    """主函数"""
    print("=== Sitemap和Collections API调试工具 ===")
    
    for site in TEST_SITES:
        debug_sitemap(site)
        test_collections_api(site)
        print("\n" + "="*80)

if __name__ == "__main__":
    main()
