#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
代理测试脚本
测试SOCKS5代理是否正常工作
"""

import requests
import json
import os
import time

def test_direct_connection():
    """测试直连"""
    print("=== 测试直连 ===")
    try:
        response = requests.get("http://httpbin.org/ip", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✓ 直连成功，IP: {data['origin']}")
            return True
        else:
            print(f"✗ 直连失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ 直连异常: {e}")
        return False

def test_proxy(proxy_config):
    """测试单个代理"""
    print(f"测试代理: {proxy_config}")
    try:
        response = requests.get(
            "http://httpbin.org/ip", 
            proxies=proxy_config,
            timeout=15
        )
        if response.status_code == 200:
            data = response.json()
            print(f"✓ 代理连接成功，IP: {data['origin']}")
            return True
        else:
            print(f"✗ 代理连接失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ 代理连接异常: {e}")
        return False

def load_proxies_from_file():
    """从文件加载代理"""
    proxy_file = "proxies.txt"
    if not os.path.exists(proxy_file):
        print(f"代理文件 {proxy_file} 不存在")
        return []
    
    proxies = []
    try:
        with open(proxy_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line and not line.startswith('#'):
                    parts = line.split(':')
                    if len(parts) >= 4:  # ip:port:user:pass
                        ip, port, user, password = parts[0], parts[1], parts[2], parts[3]
                        proxy_url = f"socks5://{user}:{password}@{ip}:{port}"
                        proxies.append({
                            'http': proxy_url,
                            'https': proxy_url,
                            'line': line_num,
                            'original': line
                        })
                    elif len(parts) == 2:  # ip:port
                        ip, port = parts[0], parts[1]
                        proxy_url = f"socks5://{ip}:{port}"
                        proxies.append({
                            'http': proxy_url,
                            'https': proxy_url,
                            'line': line_num,
                            'original': line
                        })
        
        print(f"从 {proxy_file} 加载了 {len(proxies)} 个代理")
        return proxies
        
    except Exception as e:
        print(f"加载代理文件失败: {e}")
        return []

def test_all_proxies():
    """测试所有代理"""
    print("\n=== 测试代理列表 ===")
    
    proxies = load_proxies_from_file()
    if not proxies:
        print("没有找到代理配置")
        return
    
    working_proxies = []
    failed_proxies = []
    
    for i, proxy in enumerate(proxies, 1):
        print(f"\n[{i}/{len(proxies)}] 第{proxy['line']}行: {proxy['original']}")
        
        if test_proxy({'http': proxy['http'], 'https': proxy['https']}):
            working_proxies.append(proxy)
        else:
            failed_proxies.append(proxy)
        
        # 避免请求过快
        time.sleep(1)
    
    print(f"\n=== 测试结果 ===")
    print(f"✓ 可用代理: {len(working_proxies)}")
    print(f"✗ 失败代理: {len(failed_proxies)}")
    
    if working_proxies:
        print("\n可用代理列表:")
        for proxy in working_proxies:
            print(f"  第{proxy['line']}行: {proxy['original']}")
    
    if failed_proxies:
        print("\n失败代理列表:")
        for proxy in failed_proxies:
            print(f"  第{proxy['line']}行: {proxy['original']}")

def test_shopify_access():
    """测试访问Shopify站点"""
    print("\n=== 测试Shopify访问 ===")
    
    test_url = "https://shopify.com"
    
    # 直连测试
    print("直连测试...")
    try:
        response = requests.get(test_url, timeout=10)
        print(f"✓ 直连Shopify成功，状态码: {response.status_code}")
    except Exception as e:
        print(f"✗ 直连Shopify失败: {e}")
    
    # 代理测试
    proxies = load_proxies_from_file()
    if proxies:
        print(f"\n使用第一个代理测试...")
        proxy = proxies[0]
        try:
            response = requests.get(
                test_url, 
                proxies={'http': proxy['http'], 'https': proxy['https']},
                timeout=15
            )
            print(f"✓ 代理访问Shopify成功，状态码: {response.status_code}")
        except Exception as e:
            print(f"✗ 代理访问Shopify失败: {e}")

def main():
    """主函数"""
    print("=== SOCKS5代理测试工具 ===")
    
    # 检查requests[socks]是否安装
    try:
        import requests
        # 尝试创建SOCKS代理会话
        session = requests.Session()
        print("✓ requests库支持SOCKS代理")
    except ImportError:
        print("✗ 缺少SOCKS支持，请运行: pip install requests[socks]")
        return
    
    print("\n选择测试项目:")
    print("1. 测试直连")
    print("2. 测试所有代理")
    print("3. 测试Shopify访问")
    print("4. 全部测试")
    
    choice = input("\n请选择 (1-4): ").strip()
    
    if choice == "1":
        test_direct_connection()
    elif choice == "2":
        test_all_proxies()
    elif choice == "3":
        test_shopify_access()
    elif choice == "4":
        test_direct_connection()
        test_all_proxies()
        test_shopify_access()
    else:
        print("无效选择")
        return
    
    print("\n=== 测试完成 ===")
    print("如果代理测试成功，现在可以运行主脚本:")
    print("python scrawshopify.py")

if __name__ == "__main__":
    main()
