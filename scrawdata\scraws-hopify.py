import requests
import json
import csv
import os
import time
import html
from bs4 import BeautifulSoup

# ---------------- 配置 ----------------
BASE_URL = "https://courtsidetennis.com"
DOMAIN = "courtsidetennis"
CSV_ROW_LIMIT = 50000
MAX_RETRIES = 3
OUTPUT_DIR = "./output"
PROGRESS_FILE = "./progress.json"

if not os.path.exists(OUTPUT_DIR):
    os.makedirs(OUTPUT_DIR)

CSV_HEADERS = [
    "ID", "Type", "SKU", "Name", "Published", "Is featured?", "Visibility in catalog",
    "Short description", "Description", "Tax status", "In stock?", "Stock",
    "Regular price", "Sale price", "Categories", "Tags",
    "Images", "Attribute 1 name", "Attribute 1 value(s)", "Attribute 1 visible", "Attribute 1 global",
    "Attribute 2 name", "Attribute 2 value(s)", "Attribute 2 visible", "Attribute 2 global",
    "Parent"
]

# ---------------- 工具函数 ----------------

def clean_html(raw_html: str) -> str:
    """清理 HTML 描述并解码实体"""
    raw_html = html.unescape(raw_html or "")
    soup = BeautifulSoup(raw_html, "html.parser")
    for tag in soup(["script", "style", "noscript"]):
        tag.decompose()
    for a in soup.find_all("a", href=True):
        if "shopify" in a["href"] or "ebay" in a["href"]:
            a.decompose()
    return str(soup)

def write_rows_to_csv(rows, file_index):
    """写入 CSV，追加模式"""
    filename = f"{OUTPUT_DIR}/{DOMAIN}_{file_index}.csv"
    new_file = not os.path.exists(filename)
    with open(filename, "a", newline="", encoding="utf-8-sig") as f:
        writer = csv.DictWriter(f, fieldnames=CSV_HEADERS)
        if new_file:
            writer.writeheader()
        for row in rows:
            writer.writerow(row)

def fetch_json(url, retries=MAX_RETRIES):
    """请求 JSON，自动重试，指数退避"""
    attempt = 0
    while attempt < retries:
        try:
            r = requests.get(url, headers={"User-Agent": "Mozilla/5.0"}, timeout=15)
            if r.status_code == 200:
                return r.json()
        except Exception as e:
            print(f"[!] 请求失败 {url}: {e}, 重试 {attempt+1}/{retries}")
        attempt += 1
        time.sleep(2 ** attempt)  # 指数退避
    print(f"[x] 请求失败超过最大重试次数: {url}")
    return None

def load_progress():
    if os.path.exists(PROGRESS_FILE):
        with open(PROGRESS_FILE, "r", encoding="utf-8") as f:
            return json.load(f)
    return {"last_collection": None, "processed_products": {}}

def save_progress(progress):
    with open(PROGRESS_FILE, "w", encoding="utf-8") as f:
        json.dump(progress, f, ensure_ascii=False, indent=2)

# ---------------- Shopify 数据获取 ----------------

def get_collections():
    data = fetch_json(f"{BASE_URL}/collections.json")
    if not data:
        return {}
    return {c["handle"]: c["title"] for c in data.get("collections", [])}

def get_products_from_collection(handle):
    """分页获取集合产品"""
    page = 1
    while True:
        url = f"{BASE_URL}/collections/{handle}/products.json?page={page}"
        data = fetch_json(url)
        if not data or not data.get("products"):
            break
        for product in data["products"]:
            yield product
        page += 1
        time.sleep(1)

def convert_to_woocommerce_rows(product, collection_name):
    """转换 Shopify 产品为 WooCommerce CSV 行"""
    rows = []
    product_id = product["id"]
    title = product["title"]
    body_html = clean_html(product.get("body_html", ""))
    short_desc = product.get("metafields_global_title_tag", "") or ""

    images = [img["src"] for img in product.get("images", [])]
    main_image = images[0] if images else ""
    gallery = ",".join(images[1:]) if len(images) > 1 else ""

    parent_sku = f"shopify-{product_id}"
    parent_row = {
        "ID": "",
        "Type": "variable" if len(product.get("variants", [])) > 1 else "simple",
        "SKU": parent_sku,
        "Name": title,
        "Published": "1",
        "Is featured?": "0",
        "Visibility in catalog": "visible",
        "Short description": short_desc,
        "Description": body_html,
        "Tax status": "taxable",
        "In stock?": "1",
        "Stock": 999,
        "Regular price": product["variants"][0].get("price", ""),
        "Sale price": "",
        "Categories": collection_name,
        "Tags": ",".join(product.get("tags", [])),
        "Images": main_image + ("," + gallery if gallery else ""),
        "Attribute 1 name": "Size",
        "Attribute 1 value(s)": ",".join([v.get("option1", "") for v in product.get("variants", []) if v.get("option1")]),
        "Attribute 1 visible": "1",
        "Attribute 1 global": "1",
        "Attribute 2 name": "Color",
        "Attribute 2 value(s)": ",".join([v.get("option2", "") for v in product.get("variants", []) if v.get("option2")]),
        "Attribute 2 visible": "1",
        "Attribute 2 global": "1",
        "Parent": "",
    }
    rows.append(parent_row)

    for variant in product.get("variants", []):
        child_row = {
            "ID": "",
            "Type": "variation",
            "SKU": variant.get("sku") or f"{parent_sku}-{variant['id']}",
            "Name": f"{title} - {variant.get('title','')}",
            "Published": "1",
            "Is featured?": "0",
            "Visibility in catalog": "visible",
            "Short description": "",
            "Description": "",
            "Tax status": "taxable",
            "In stock?": "1",
            "Stock": 999,
            "Regular price": variant.get("price", ""),
            "Sale price": "",
            "Categories": "",
            "Tags": "",
            "Images": "",
            "Attribute 1 name": "Size",
            "Attribute 1 value(s)": variant.get("option1", ""),
            "Attribute 1 visible": "1",
            "Attribute 1 global": "1",
            "Attribute 2 name": "Color",
            "Attribute 2 value(s)": variant.get("option2", ""),
            "Attribute 2 visible": "1",
            "Attribute 2 global": "1",
            "Parent": parent_sku,
        }
        rows.append(child_row)
    return rows

# ---------------- 主采集流程 ----------------

def main():
    file_index = 1
    row_count = 0
    progress = load_progress()

    collections = get_collections()
    for handle, cname in collections.items():
        if progress["last_collection"] and handle < progress["last_collection"]:
            continue

        print(f"[+] 采集分类: {cname}")
        processed_count = progress["processed_products"].get(handle, 0)

        for idx, product in enumerate(get_products_from_collection(handle), 1):
            if idx <= processed_count:
                continue  # 跳过已采集产品

            rows = convert_to_woocommerce_rows(product, cname)

            if row_count + len(rows) > CSV_ROW_LIMIT:
                file_index += 1
                row_count = 0

            write_rows_to_csv(rows, file_index)
            row_count += len(rows)

            # 更新进度
            progress["last_collection"] = handle
            progress["processed_products"][handle] = idx
            save_progress(progress)

            time.sleep(0.5)

    print(f"✅ 采集完成，生成 CSV 文件 {file_index} 个")

# ---------------- nohup / 守护进程支持 ----------------
if __name__ == "__main__":
    while True:
        try:
            main()
            break
        except Exception as e:
            print(f"[x] 脚本异常: {e}, 5秒后重启...")
            time.sleep(5)
