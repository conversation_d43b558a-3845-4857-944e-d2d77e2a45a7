#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
使用示例脚本
演示如何使用改进后的Shopify采集器
"""

import json
import os

def create_example_config():
    """创建示例配置"""
    print("=== 创建示例配置文件 ===")
    
    # 创建config.json
    config = {
        "proxy": None,  # 如果需要代理，设置为 {"http": "*******************:port", "https": "*******************:port"}
        "retry_limit": 3,
        "sleep_time": 2,
        "max_workers": 3,  # 建议不超过5
        "timeout": 15,
        "user_agents": [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        ]
    }
    
    with open("config.json", "w", encoding="utf-8") as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    print("✓ config.json 已创建")
    
    # 创建sites.json
    sites = [
        {
            "name": "示例商店1",
            "sitemap": "https://example-store1.myshopify.com/sitemap.xml",
            "enabled": True,
            "description": "第一个示例商店"
        },
        {
            "name": "示例商店2", 
            "sitemap": "https://example-store2.myshopify.com/sitemap.xml",
            "enabled": True,
            "description": "第二个示例商店"
        },
        {
            "name": "禁用的商店",
            "sitemap": "https://disabled-store.myshopify.com/sitemap.xml",
            "enabled": False,
            "description": "这个商店被禁用，不会被采集"
        }
    ]
    
    with open("sites.json", "w", encoding="utf-8") as f:
        json.dump(sites, f, indent=2, ensure_ascii=False)
    print("✓ sites.json 已创建")

def show_usage_examples():
    """显示使用示例"""
    print("\n=== 使用示例 ===")
    
    print("1. 单站点采集:")
    print("   python scrawshopify.py")
    print("   选择模式 1，然后输入sitemap URL")
    print("   例如: https://your-store.myshopify.com/sitemap.xml")
    
    print("\n2. 多站点采集:")
    print("   - 编辑 sites.json 添加要采集的站点")
    print("   - python scrawshopify.py")
    print("   - 选择模式 2")
    print("   - 设置并发数（建议1-3）")
    
    print("\n3. 配置代理:")
    print("   编辑 config.json:")
    print('   "proxy": {')
    print('     "http": "**********************:port",')
    print('     "https": "**********************:port"')
    print('   }')
    
    print("\n4. 调整采集参数:")
    print("   - retry_limit: 重试次数")
    print("   - sleep_time: 重试间隔")
    print("   - max_workers: 最大并发数")
    print("   - timeout: 请求超时时间")

def show_file_structure():
    """显示文件结构"""
    print("\n=== 文件结构说明 ===")
    
    structure = """
scrawdata/
├── scrawshopify.py          # 主采集脚本
├── config.json              # 全局配置文件
├── sites.json               # 站点配置文件
├── requirements.txt         # 依赖列表
├── install_deps.py          # 依赖安装脚本
├── simple_test.py           # 简单测试脚本
├── example_usage.py         # 使用示例（本文件）
├── README.md                # 详细说明文档
├── output/                  # CSV输出目录
│   ├── site1_1.csv         # 站点1的CSV文件
│   └── site2_1.csv         # 站点2的CSV文件
├── collector.log            # 详细日志文件
└── seen_products.json       # 已采集产品记录
    """
    print(structure)

def show_troubleshooting():
    """显示故障排除"""
    print("\n=== 常见问题解决 ===")
    
    print("1. 导入错误:")
    print("   - 运行: python install_deps.py")
    print("   - 或手动: pip install requests beautifulsoup4 tqdm")
    
    print("\n2. 连接超时:")
    print("   - 检查网络连接")
    print("   - 增加config.json中的timeout值")
    print("   - 使用代理")
    
    print("\n3. 被封IP:")
    print("   - 降低max_workers值")
    print("   - 增加sleep_time值")
    print("   - 使用代理池")
    
    print("\n4. 数据不完整:")
    print("   - 检查sitemap URL是否正确")
    print("   - 查看collector.log了解详细错误")
    
    print("\n5. 内存不足:")
    print("   - 减少max_workers值")
    print("   - 分批处理站点")

def main():
    """主函数"""
    print("=== Shopify采集器使用指南 ===")
    
    print("选择操作:")
    print("1. 创建示例配置文件")
    print("2. 显示使用示例")
    print("3. 显示文件结构")
    print("4. 显示故障排除")
    print("5. 全部显示")
    
    choice = input("\n请选择 (1-5): ").strip()
    
    if choice == "1":
        create_example_config()
    elif choice == "2":
        show_usage_examples()
    elif choice == "3":
        show_file_structure()
    elif choice == "4":
        show_troubleshooting()
    elif choice == "5":
        create_example_config()
        show_usage_examples()
        show_file_structure()
        show_troubleshooting()
    else:
        print("无效选择")
        return
    
    print("\n=== 下一步 ===")
    print("1. 运行测试: python simple_test.py")
    print("2. 开始采集: python scrawshopify.py")
    print("3. 查看文档: 阅读 README.md")

if __name__ == "__main__":
    main()
