# 代理和站点配置指南

## 🔧 SOCKS5代理配置

### 1. 代理文件格式 (proxies.txt)

支持以下格式：
```
# 带认证的SOCKS5代理
IP:端口:用户名:密码
************:5727:dxgetpjh:nsueipnrildn

# 不需要认证的代理
IP:端口
123.456.789.0:1234

# 注释行
# 这是注释，会被忽略
```

### 2. 配置文件设置 (config.json)

```json
{
  "proxy_file": "proxies.txt",        // 代理文件路径
  "proxy_type": "socks5",             // 代理类型: socks5 或 http
  "use_proxy_rotation": true,         // 是否轮换代理
  "max_workers": 3,                   // 建议降低并发数
  "retry_limit": 3,                   // 重试次数
  "sleep_time": 2                     // 重试间隔
}
```

### 3. 代理使用模式

#### 模式1: 代理轮换 (推荐)
- 设置 `"use_proxy_rotation": true`
- 脚本会自动轮换使用不同代理
- 适合大量采集，降低被封风险

#### 模式2: 固定代理
- 设置 `"use_proxy_rotation": false`
- 只使用第一个代理
- 适合测试或小量采集

#### 模式3: 不使用代理
- 删除或清空 `proxies.txt`
- 或设置 `"use_proxy_rotation": false` 且不设置 `"proxy"`

## 📁 站点配置

### 1. sites.txt 格式 (推荐)

```
# 格式1: 站点名称|sitemap URL
我的商店1|https://store1.myshopify.com/sitemap.xml
我的商店2|https://store2.myshopify.com/sitemap.xml

# 格式2: 只有URL
https://store3.myshopify.com/sitemap.xml

# 注释掉不需要的站点
# 暂停商店|https://paused-store.myshopify.com/sitemap.xml
```

### 2. sites.json 格式 (传统)

```json
[
  {
    "name": "商店1",
    "sitemap": "https://store1.myshopify.com/sitemap.xml",
    "enabled": true
  },
  {
    "name": "商店2",
    "sitemap": "https://store2.myshopify.com/sitemap.xml", 
    "enabled": false
  }
]
```

## 🚀 使用流程

### 1. 准备代理
```bash
# 编辑代理文件
notepad proxies.txt

# 添加你的SOCKS5代理
************:5727:dxgetpjh:nsueipnrildn
*************:6455:dxgetpjh:nsueipnrildn
```

### 2. 准备站点列表
```bash
# 编辑站点文件
notepad sites.txt

# 添加你的站点
我的商店|https://my-store.myshopify.com/sitemap.xml
```

### 3. 安装依赖
```bash
pip install requests[socks] beautifulsoup4 tqdm
```

### 4. 运行采集
```bash
python scrawshopify.py
# 选择模式2 (多站点采集)
```

## ⚙️ 高级配置

### 代理测试
脚本会自动测试代理可用性，失败的代理会被跳过。

### 错误处理
- 代理连接失败：自动切换下一个代理
- 代理被封：记录警告并继续
- 网络超时：自动重试

### 性能优化
- 降低 `max_workers` 避免过多并发
- 增加 `sleep_time` 减少请求频率
- 使用高质量代理提高成功率

## 🔍 故障排除

### 代理相关问题

1. **代理连接失败**
   ```
   解决方案:
   - 检查代理格式是否正确
   - 验证代理是否有效
   - 确认用户名密码正确
   ```

2. **SOCKS5不支持**
   ```bash
   # 安装SOCKS5支持
   pip install requests[socks]
   ```

3. **代理被封**
   ```
   解决方案:
   - 更换代理IP
   - 降低采集频率
   - 使用住宅IP代理
   ```

### 站点配置问题

1. **sitemap无法访问**
   ```
   检查项:
   - URL是否正确
   - 站点是否启用sitemap
   - 是否需要特殊权限
   ```

2. **没有产品URL**
   ```
   可能原因:
   - sitemap格式不标准
   - 产品URL路径不是/products/
   - 站点没有产品
   ```

## 📊 监控和日志

### 日志文件
- `collector.log`: 详细采集日志
- 包含代理使用情况
- 记录错误和警告信息

### 实时监控
- 进度条显示采集进度
- 控制台输出关键信息
- 代理切换提示

## 💡 最佳实践

1. **代理管理**
   - 使用多个不同地区的代理
   - 定期更新代理列表
   - 监控代理使用情况

2. **采集策略**
   - 分批次采集大量站点
   - 避免高峰时段采集
   - 合理设置并发数

3. **数据管理**
   - 定期备份采集数据
   - 清理重复产品记录
   - 验证数据完整性
