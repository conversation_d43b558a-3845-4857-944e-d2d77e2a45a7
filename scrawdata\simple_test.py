#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单测试脚本 - 验证基本功能
"""

def test_imports():
    """测试导入"""
    try:
        import requests
        print("✓ requests 导入成功")
    except ImportError:
        print("✗ requests 导入失败")
        return False
    
    try:
        from bs4 import BeautifulSoup
        print("✓ BeautifulSoup 导入成功")
    except ImportError:
        print("✗ BeautifulSoup 导入失败")
        return False
    
    try:
        import json
        import os
        import csv
        print("✓ 标准库导入成功")
    except ImportError:
        print("✗ 标准库导入失败")
        return False
    
    return True

def test_config_creation():
    """测试配置文件创建"""
    import json
    import os
    
    config = {
        "proxy": None,
        "retry_limit": 3,
        "sleep_time": 2,
        "max_workers": 3,
        "timeout": 15
    }
    
    try:
        with open("test_config.json", "w", encoding="utf-8") as f:
            json.dump(config, f, indent=2)
        
        # 读取验证
        with open("test_config.json", "r", encoding="utf-8") as f:
            loaded_config = json.load(f)
        
        assert loaded_config["retry_limit"] == 3
        print("✓ 配置文件创建和读取成功")
        
        # 清理
        os.remove("test_config.json")
        return True
        
    except Exception as e:
        print(f"✗ 配置文件测试失败: {e}")
        return False

def test_html_cleaning():
    """测试HTML清洗"""
    try:
        from bs4 import BeautifulSoup
        
        html = '<div><p>测试</p><script>alert("bad")</script></div>'
        soup = BeautifulSoup(html, 'html.parser')
        
        # 移除script标签
        for script in soup.find_all('script'):
            script.decompose()
        
        result = str(soup)
        assert '<script>' not in result
        assert '<p>测试</p>' in result
        
        print("✓ HTML清洗功能正常")
        return True
        
    except Exception as e:
        print(f"✗ HTML清洗测试失败: {e}")
        return False

def test_csv_writing():
    """测试CSV写入"""
    import csv
    import os
    
    try:
        test_data = [
            {"Name": "产品1", "Price": "29.99", "SKU": "TEST001"},
            {"Name": "产品2", "Price": "39.99", "SKU": "TEST002"}
        ]
        
        with open("test_output.csv", "w", newline="", encoding="utf-8") as f:
            writer = csv.DictWriter(f, fieldnames=["Name", "Price", "SKU"])
            writer.writeheader()
            writer.writerows(test_data)
        
        # 验证文件
        with open("test_output.csv", "r", encoding="utf-8") as f:
            content = f.read()
            assert "产品1" in content
            assert "29.99" in content
        
        print("✓ CSV写入功能正常")
        
        # 清理
        os.remove("test_output.csv")
        return True
        
    except Exception as e:
        print(f"✗ CSV写入测试失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("=== 简单功能测试 ===")
    
    tests = [
        test_imports,
        test_config_creation,
        test_html_cleaning,
        test_csv_writing
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有基础功能测试通过！")
        print("现在可以尝试运行主脚本: python scrawshopify.py")
    else:
        print("❌ 部分测试失败，请检查依赖安装")
        print("建议运行: pip install requests beautifulsoup4")

if __name__ == "__main__":
    main()
