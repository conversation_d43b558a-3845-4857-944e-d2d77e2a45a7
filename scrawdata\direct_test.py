#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
直连测试脚本
测试两个站点的直连访问
"""

import requests
import json
import csv
import os
import time
from bs4 import BeautifulSoup
from xml.etree import ElementTree as ET

# 测试站点
TEST_SITES = [
    {"name": "courtsidetennis", "sitemap": "https://courtsidetennis.com/sitemap.xml"},
    {"name": "solartopstore", "sitemap": "https://www.solartopstore.com/sitemap.xml"}
]

def fetch_url(url, timeout=15):
    """获取URL内容"""
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=timeout)
        print(f"  状态码: {response.status_code}")
        if response.status_code == 200:
            return response.text
        else:
            return None
    except Exception as e:
        print(f"  请求失败: {e}")
        return None

def parse_sitemap(sitemap_url):
    """解析sitemap获取产品URL"""
    print(f"\n解析sitemap: {sitemap_url}")
    
    content = fetch_url(sitemap_url)
    if not content:
        return []
    
    print(f"  sitemap内容长度: {len(content)}")
    
    urls = []
    try:
        root = ET.fromstring(content)
        
        # 尝试标准命名空间
        for url_elem in root.findall(".//{http://www.sitemaps.org/schemas/sitemap/0.9}loc"):
            url = url_elem.text
            if url and ('/products/' in url or '/product/' in url):
                urls.append(url)
        
        # 如果没找到，尝试不使用命名空间
        if not urls:
            for url_elem in root.findall(".//loc"):
                url = url_elem.text
                if url and ('/products/' in url or '/product/' in url):
                    urls.append(url)
                    
        print(f"  找到产品URL: {len(urls)}")
        if urls:
            print(f"  示例URL: {urls[0]}")
            
    except Exception as e:
        print(f"  sitemap解析失败: {e}")
        # 尝试查看原始内容的前500字符
        print(f"  原始内容预览: {content[:500]}...")
        return []
    
    return urls

def get_product_json(product_url):
    """获取产品JSON数据"""
    json_url = product_url.rstrip('/') + '.json'
    print(f"    获取JSON: {json_url}")
    
    content = fetch_url(json_url)
    if not content:
        return None
    
    try:
        data = json.loads(content)
        product = data.get('product', {})
        if product:
            print(f"    产品标题: {product.get('title', 'N/A')}")
        return product
    except Exception as e:
        print(f"    JSON解析失败: {e}")
        return None

def test_site(site_info, max_products=10):
    """测试单个站点"""
    site_name = site_info['name']
    sitemap_url = site_info['sitemap']
    
    print(f"\n{'='*50}")
    print(f"测试站点: {site_name}")
    print(f"{'='*50}")
    
    # 解析sitemap
    urls = parse_sitemap(sitemap_url)
    if not urls:
        print("❌ 未找到产品URL")
        return {'success': 0, 'failed': 1, 'products': []}
    
    # 限制测试数量
    test_urls = urls[:max_products]
    print(f"\n开始测试前 {len(test_urls)} 个产品:")
    
    success_count = 0
    failed_count = 0
    products = []
    
    for i, url in enumerate(test_urls, 1):
        print(f"\n  [{i}/{len(test_urls)}] {url}")
        
        try:
            product = get_product_json(url)
            if product and product.get('title'):
                title = product.get('title', '')
                handle = product.get('handle', '')
                variants = product.get('variants', [])
                price = variants[0].get('price', '') if variants else ''
                
                products.append({
                    'title': title,
                    'handle': handle,
                    'price': price,
                    'url': url
                })
                
                print(f"    ✅ 成功: {title[:50]}...")
                success_count += 1
            else:
                print(f"    ❌ 无效产品数据")
                failed_count += 1
        except Exception as e:
            print(f"    ❌ 异常: {e}")
            failed_count += 1
        
        # 延迟避免请求过快
        time.sleep(1)
    
    result = {
        'success': success_count,
        'failed': failed_count,
        'products': products
    }
    
    print(f"\n站点 {site_name} 测试结果:")
    print(f"  成功: {success_count}")
    print(f"  失败: {failed_count}")
    
    return result

def save_results_to_csv(site_name, products):
    """保存结果到CSV"""
    if not products:
        print(f"  {site_name}: 没有数据可保存")
        return
    
    os.makedirs('output', exist_ok=True)
    filename = f"output/{site_name}_direct_test.csv"
    
    with open(filename, 'w', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=['title', 'handle', 'price', 'url'])
        writer.writeheader()
        writer.writerows(products)
    
    print(f"  结果已保存到: {filename}")

def main():
    """主函数"""
    print("=== Shopify站点直连测试 ===")
    print("测试站点:")
    for site in TEST_SITES:
        print(f"  - {site['name']}: {site['sitemap']}")
    
    all_results = []
    
    for site in TEST_SITES:
        result = test_site(site, max_products=10)
        save_results_to_csv(site['name'], result['products'])
        all_results.append({
            'site': site['name'],
            'result': result
        })
    
    # 显示汇总
    print(f"\n{'='*50}")
    print("测试汇总:")
    print(f"{'='*50}")
    
    total_success = 0
    total_failed = 0
    
    for result in all_results:
        site_name = result['site']
        success = result['result']['success']
        failed = result['result']['failed']
        
        total_success += success
        total_failed += failed
        
        print(f"{site_name}: 成功 {success}, 失败 {failed}")
    
    print(f"\n总计: 成功 {total_success}, 失败 {total_failed}")
    print(f"测试完成！请检查output目录下的CSV文件。")

if __name__ == "__main__":
    main()
