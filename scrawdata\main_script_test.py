#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
主脚本功能测试
测试采集20条产品，验证数据完整性和HTML处理
"""

import sys
import os
import json
import csv
from datetime import datetime

# 导入主脚本功能
try:
    from scrawshopify import (
        load_config, process_single_site, load_sites_config,
        get_collections, get_products_from_collection, 
        product_to_csv_rows, CSVWriter
    )
except ImportError as e:
    print(f"导入主脚本失败: {e}")
    sys.exit(1)

def test_direct_connection():
    """测试直连模式"""
    print("=== 测试1: 直连模式 ===")
    
    # 确保禁用代理
    config = load_config()
    config['enable_proxy'] = False
    
    # 测试站点
    sites = [
        {"name": "网球用品店", "sitemap": "https://courtsidetennis.com/sitemap.xml"},
        {"name": "太阳能商店", "sitemap": "https://www.solartopstore.com/sitemap.xml"}
    ]
    
    results = []
    
    for site in sites:
        print(f"\n--- 直连测试: {site['name']} ---")
        
        try:
            # 使用Collections API采集20个产品
            base_url = site['sitemap'].replace('/sitemap.xml', '')
            collections = get_collections(base_url)
            
            if collections:
                print(f"找到 {len(collections)} 个集合，使用Collections API")
                
                # 从前2个集合各采集10个产品
                all_products = []
                for i, collection in enumerate(collections[:2]):
                    collection_handle = collection.get('handle', '')
                    print(f"  采集集合: {collection.get('title', collection_handle)}")
                    
                    products = get_products_from_collection(base_url, collection_handle, 10)
                    all_products.extend(products)
                    
                    if len(all_products) >= 20:
                        all_products = all_products[:20]
                        break
                
                # 转换为CSV格式
                csv_rows = []
                for product in all_products:
                    try:
                        rows = product_to_csv_rows(product)
                        csv_rows.extend(rows)
                    except Exception as e:
                        print(f"    产品转换失败: {e}")
                
                # 保存结果
                save_test_results(site['name'], 'direct', csv_rows, all_products)
                
                results.append({
                    'site': site['name'],
                    'mode': 'direct',
                    'success': len(csv_rows),
                    'raw_products': len(all_products)
                })
                
            else:
                print("  Collections API不可用，跳过")
                results.append({
                    'site': site['name'],
                    'mode': 'direct',
                    'success': 0,
                    'raw_products': 0
                })
                
        except Exception as e:
            print(f"  直连测试失败: {e}")
            results.append({
                'site': site['name'],
                'mode': 'direct',
                'success': 0,
                'raw_products': 0,
                'error': str(e)
            })
    
    return results

def test_proxy_connection():
    """测试代理模式"""
    print("\n=== 测试2: Webshare代理模式 ===")
    
    # 启用代理
    config = load_config()
    config['enable_proxy'] = True
    config['proxy_type'] = 'webshare'
    
    # 测试站点
    sites = [
        {"name": "网球用品店", "sitemap": "https://courtsidetennis.com/sitemap.xml"},
        {"name": "太阳能商店", "sitemap": "https://www.solartopstore.com/sitemap.xml"}
    ]
    
    results = []
    
    for site in sites:
        print(f"\n--- 代理测试: {site['name']} ---")
        
        try:
            # 使用Collections API采集20个产品
            base_url = site['sitemap'].replace('/sitemap.xml', '')
            collections = get_collections(base_url)
            
            if collections:
                print(f"找到 {len(collections)} 个集合，使用Collections API")
                
                # 从前2个集合各采集10个产品
                all_products = []
                for i, collection in enumerate(collections[:2]):
                    collection_handle = collection.get('handle', '')
                    print(f"  采集集合: {collection.get('title', collection_handle)}")
                    
                    products = get_products_from_collection(base_url, collection_handle, 10)
                    all_products.extend(products)
                    
                    if len(all_products) >= 20:
                        all_products = all_products[:20]
                        break
                
                # 转换为CSV格式
                csv_rows = []
                for product in all_products:
                    try:
                        rows = product_to_csv_rows(product)
                        csv_rows.extend(rows)
                    except Exception as e:
                        print(f"    产品转换失败: {e}")
                
                # 保存结果
                save_test_results(site['name'], 'proxy', csv_rows, all_products)
                
                results.append({
                    'site': site['name'],
                    'mode': 'proxy',
                    'success': len(csv_rows),
                    'raw_products': len(all_products)
                })
                
            else:
                print("  Collections API不可用，跳过")
                results.append({
                    'site': site['name'],
                    'mode': 'proxy',
                    'success': 0,
                    'raw_products': 0
                })
                
        except Exception as e:
            print(f"  代理测试失败: {e}")
            results.append({
                'site': site['name'],
                'mode': 'proxy',
                'success': 0,
                'raw_products': 0,
                'error': str(e)
            })
    
    return results

def save_test_results(site_name, mode, csv_rows, raw_products):
    """保存测试结果"""
    os.makedirs('output', exist_ok=True)
    
    # 保存CSV文件
    if csv_rows:
        csv_filename = f"output/{site_name}_{mode}_20products.csv"
        
        # 获取字段名
        fieldnames = csv_rows[0].keys() if csv_rows else []
        
        with open(csv_filename, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(csv_rows)
        
        print(f"  ✅ CSV已保存: {csv_filename}")
    
    # 保存原始JSON数据（用于分析）
    if raw_products:
        json_filename = f"output/{site_name}_{mode}_raw_data.json"
        
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(raw_products, f, indent=2, ensure_ascii=False)
        
        print(f"  ✅ 原始数据已保存: {json_filename}")

def analyze_results():
    """分析测试结果"""
    print(f"\n{'='*60}")
    print("数据质量分析")
    print(f"{'='*60}")
    
    # 分析CSV文件
    csv_files = [
        'output/网球用品店_direct_20products.csv',
        'output/网球用品店_proxy_20products.csv',
        'output/太阳能商店_direct_20products.csv',
        'output/太阳能商店_proxy_20products.csv'
    ]
    
    for csv_file in csv_files:
        if os.path.exists(csv_file):
            print(f"\n--- 分析: {csv_file} ---")
            analyze_csv_file(csv_file)

def analyze_csv_file(csv_file):
    """分析单个CSV文件"""
    try:
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            rows = list(reader)
        
        print(f"总行数: {len(rows)}")
        
        # 统计产品类型
        types = {}
        for row in rows:
            product_type = row.get('Type', 'unknown')
            types[product_type] = types.get(product_type, 0) + 1
        
        print(f"产品类型分布: {types}")
        
        # 检查数据完整性
        complete_products = 0
        has_price = 0
        has_description = 0
        has_images = 0
        
        for row in rows:
            if row.get('Name') and row.get('SKU'):
                complete_products += 1
            
            if row.get('Regular price'):
                has_price += 1
            
            if row.get('Description'):
                has_description += 1
            
            if row.get('Images'):
                has_images += 1
        
        print(f"数据完整性:")
        print(f"  完整产品: {complete_products}/{len(rows)} ({complete_products/len(rows)*100:.1f}%)")
        print(f"  有价格: {has_price}/{len(rows)} ({has_price/len(rows)*100:.1f}%)")
        print(f"  有描述: {has_description}/{len(rows)} ({has_description/len(rows)*100:.1f}%)")
        print(f"  有图片: {has_images}/{len(rows)} ({has_images/len(rows)*100:.1f}%)")
        
        # 检查HTML标签处理
        html_analysis = analyze_html_descriptions(rows)
        print(f"HTML处理分析: {html_analysis}")
        
    except Exception as e:
        print(f"分析失败: {e}")

def analyze_html_descriptions(rows):
    """分析HTML描述处理"""
    html_tags_found = set()
    clean_descriptions = 0
    
    for row in rows:
        description = row.get('Description', '')
        if description:
            # 检查是否包含HTML标签
            import re
            tags = re.findall(r'<(\w+)', description)
            html_tags_found.update(tags)
            
            # 检查是否是干净的描述
            if not re.search(r'<script|<style|<link', description, re.IGNORECASE):
                clean_descriptions += 1
    
    return {
        'total_with_description': sum(1 for row in rows if row.get('Description')),
        'clean_descriptions': clean_descriptions,
        'html_tags_found': list(html_tags_found),
        'clean_percentage': clean_descriptions / max(1, sum(1 for row in rows if row.get('Description'))) * 100
    }

def main():
    """主函数"""
    print("=== 主脚本功能完整测试 ===")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("目标: 每个站点采集20个产品，测试直连和代理模式")
    
    # 测试1: 直连模式
    direct_results = test_direct_connection()
    
    # 测试2: 代理模式  
    proxy_results = test_proxy_connection()
    
    # 显示汇总结果
    print(f"\n{'='*60}")
    print("测试汇总结果")
    print(f"{'='*60}")
    
    all_results = direct_results + proxy_results
    
    for result in all_results:
        site = result['site']
        mode = result['mode']
        success = result.get('success', 0)
        raw_count = result.get('raw_products', 0)
        
        status = "✅" if success > 0 else "❌"
        print(f"{status} {site} ({mode}): CSV行数 {success}, 原始产品 {raw_count}")
        
        if 'error' in result:
            print(f"    错误: {result['error']}")
    
    # 分析结果
    analyze_results()
    
    print(f"\n{'='*60}")
    print("测试完成！请检查output目录下的文件:")
    print("- *_20products.csv: WooCommerce导入格式")
    print("- *_raw_data.json: 原始产品数据")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
