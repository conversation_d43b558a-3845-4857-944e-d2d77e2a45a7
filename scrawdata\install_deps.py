#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
依赖安装脚本
"""

import subprocess
import sys
import os

def install_package(package):
    """安装单个包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def check_package(package):
    """检查包是否已安装"""
    try:
        __import__(package)
        return True
    except ImportError:
        return False

def main():
    """主函数"""
    print("=== Shopify采集器依赖安装 ===")
    
    # 需要的包
    packages = {
        "requests": "requests>=2.25.0",
        "bs4": "beautifulsoup4>=4.9.0", 
        "tqdm": "tqdm>=4.60.0"
    }
    
    missing_packages = []
    
    # 检查已安装的包
    print("检查依赖...")
    for import_name, package_name in packages.items():
        if check_package(import_name):
            print(f"✓ {import_name} 已安装")
        else:
            print(f"✗ {import_name} 未安装")
            missing_packages.append(package_name)
    
    if not missing_packages:
        print("\n🎉 所有依赖都已安装！")
        return
    
    # 安装缺失的包
    print(f"\n需要安装 {len(missing_packages)} 个包:")
    for package in missing_packages:
        print(f"  - {package}")
    
    confirm = input("\n是否现在安装? (y/n): ").lower().strip()
    if confirm != 'y':
        print("安装已取消")
        return
    
    print("\n开始安装...")
    success_count = 0
    
    for package in missing_packages:
        print(f"\n安装 {package}...")
        if install_package(package):
            print(f"✓ {package} 安装成功")
            success_count += 1
        else:
            print(f"✗ {package} 安装失败")
    
    print(f"\n安装完成: {success_count}/{len(missing_packages)} 成功")
    
    if success_count == len(missing_packages):
        print("🎉 所有依赖安装成功！")
        print("现在可以运行: python scrawshopify.py")
    else:
        print("❌ 部分依赖安装失败")
        print("请手动运行: pip install requests beautifulsoup4 tqdm")

if __name__ == "__main__":
    main()
