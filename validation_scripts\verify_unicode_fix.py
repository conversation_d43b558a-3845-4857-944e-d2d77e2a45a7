#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证Unicode修复效果
"""

import pandas as pd

def verify_unicode_fix():
    print('验证Unicode修复效果')
    print('='*40)
    
    file_path = 'woocommerce_output_final/woocommerce_lampenwelt-de_final.csv'
    df = pd.read_csv(file_path, encoding='utf-8-sig')
    
    # 查找包含德语字符的内容
    german_chars = ['ä', 'ö', 'ü', 'ß', 'Ä', 'Ö', 'Ü']
    found_examples = []
    
    for col in ['Name', 'Categories', 'Tags', 'Attribute 1 value(s)']:
        if col in df.columns:
            for idx, value in df[col].items():
                if pd.notna(value):
                    value_str = str(value)
                    if any(char in value_str for char in german_chars):
                        found_examples.append({
                            'field': col,
                            'value': value_str,
                            'row': idx + 1
                        })
                        if len(found_examples) >= 10:
                            break
            if len(found_examples) >= 10:
                break
    
    print(f'找到 {len(found_examples)} 个德语字符示例:')
    print()
    
    for i, example in enumerate(found_examples[:5]):
        print(f'{i+1}. {example["field"]} (行{example["row"]}):')
        print(f'   {example["value"]}')
        
        # 检查是否正确显示德语字符
        has_german = any(char in example['value'] for char in german_chars)
        if has_german:
            print('   ✅ 德语字符正确显示')
        else:
            print('   ℹ️ 无德语字符')
        print()
    
    # 检查是否还有Unicode编码问题
    unicode_issues = 0
    for col in df.columns:
        for value in df[col].dropna():
            if '\\u00' in str(value):
                unicode_issues += 1
    
    print(f'剩余Unicode编码问题: {unicode_issues}')
    
    if unicode_issues == 0:
        print('✅ 所有Unicode编码问题已修复!')
    else:
        print(f'⚠️ 仍有 {unicode_issues} 个Unicode编码问题')
    
    print()
    print('🎉 验证完成!')

if __name__ == "__main__":
    verify_unicode_fix()
