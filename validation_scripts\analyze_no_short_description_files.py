#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析没有短描述的CSV文件，生成适合的短描述
"""

import pandas as pd
import re
from collections import Counter

def analyze_file_without_short_desc(file_path, file_name):
    """分析没有短描述的文件"""
    print(f'📊 分析 {file_name}')
    print('='*50)
    
    try:
        df = pd.read_csv(file_path, encoding='utf-8-sig')
        
        print(f'总产品数: {len(df):,}')
        print(f'字段数量: {len(df.columns)}')
        
        # 检查短描述情况
        if 'Short description' in df.columns:
            short_desc = df['Short description']
            empty_count = short_desc.isna().sum() + (short_desc == '').sum()
            has_desc_count = len(df) - empty_count
            
            print(f'\n短描述情况:')
            print(f'  有短描述: {has_desc_count}个 ({has_desc_count/len(df)*100:.1f}%)')
            print(f'  空短描述: {empty_count}个 ({empty_count/len(df)*100:.1f}%)')
            
            if has_desc_count > 0:
                print(f'  现有短描述示例:')
                for i, desc in enumerate(short_desc.dropna().head(3)):
                    if desc != '':
                        print(f'    {i+1}. {str(desc)[:60]}...')
        
        # 分析产品名称特点
        print(f'\n📦 产品名称分析:')
        names = df['Name'].dropna()
        if len(names) > 0:
            name_lengths = [len(str(name)) for name in names.head(1000)]
            avg_length = sum(name_lengths) / len(name_lengths)
            print(f'  平均长度: {avg_length:.1f} 字符')
            
            # 提取关键词
            all_words = []
            for name in names.head(1000):
                words = str(name).lower().split()
                # 过滤掉数字和短词
                meaningful_words = [word for word in words if len(word) > 2 and not word.isdigit()]
                all_words.extend(meaningful_words[:3])  # 每个产品取前3个词
            
            word_counter = Counter(all_words)
            print(f'  最常见关键词:')
            for word, count in word_counter.most_common(10):
                print(f'    "{word}" - {count}次')
        
        # 分析分类
        print(f'\n🏷️ 分类分析:')
        if 'Categories' in df.columns:
            categories = df['Categories'].dropna()
            if len(categories) > 0:
                unique_cats = categories.nunique()
                print(f'  唯一分类数: {unique_cats}')
                
                # 显示主要分类
                cat_counts = categories.value_counts().head(8)
                print(f'  主要分类:')
                for cat, count in cat_counts.items():
                    print(f'    "{cat}" - {count}个产品')
        
        # 分析Tags
        print(f'\n🏷️ Tags分析:')
        if 'Tags' in df.columns:
            tags_data = df['Tags'].dropna()
            if len(tags_data) > 0:
                print(f'  有Tags的产品: {len(tags_data):,}个')
                
                # 分析Tags内容
                all_tags = []
                for tags in tags_data.head(500):
                    tag_str = str(tags)
                    if ',' in tag_str:
                        individual_tags = [tag.strip() for tag in tag_str.split(',')]
                    else:
                        individual_tags = [tag_str.strip()]
                    all_tags.extend(individual_tags)
                
                if all_tags:
                    tag_counter = Counter(all_tags)
                    print(f'  最常见Tags:')
                    for tag, count in tag_counter.most_common(8):
                        if tag and len(tag.strip()) > 1:
                            print(f'    "{tag}" - {count}次')
        
        # 分析价格
        print(f'\n💰 价格分析:')
        if 'Regular price' in df.columns:
            prices = df['Regular price'].dropna()
            if len(prices) > 0:
                price_values = []
                for price in prices.head(1000):
                    try:
                        price_val = float(price)
                        if price_val > 0:
                            price_values.append(price_val)
                    except:
                        pass
                
                if price_values:
                    avg_price = sum(price_values) / len(price_values)
                    min_price = min(price_values)
                    max_price = max(price_values)
                    
                    print(f'  价格范围: €{min_price:.2f} - €{max_price:.2f}')
                    print(f'  平均价格: €{avg_price:.2f}')
                    
                    # 价格区间分布
                    price_ranges = {
                        '€0-50': len([p for p in price_values if 0 < p <= 50]),
                        '€50-200': len([p for p in price_values if 50 < p <= 200]),
                        '€200-1000': len([p for p in price_values if 200 < p <= 1000]),
                        '€1000+': len([p for p in price_values if p > 1000])
                    }
                    
                    print(f'  价格区间分布:')
                    for range_name, count in price_ranges.items():
                        if count > 0:
                            percentage = count / len(price_values) * 100
                            print(f'    {range_name}: {count}个 ({percentage:.1f}%)')
        
        # 显示产品示例
        print(f'\n📋 产品示例:')
        for i in range(min(5, len(df))):
            row = df.iloc[i]
            print(f'  {i+1}. {str(row["Name"])[:50]}...')
            if 'Categories' in df.columns and pd.notna(row['Categories']):
                print(f'     分类: {str(row["Categories"])[:40]}...')
            if 'Regular price' in df.columns and pd.notna(row['Regular price']):
                print(f'     价格: €{row["Regular price"]}')
        
        return {
            'file_name': file_name,
            'product_count': len(df),
            'has_short_desc': has_desc_count if 'Short description' in df.columns else 0,
            'avg_price': avg_price if 'price_values' in locals() and price_values else 0,
            'main_categories': cat_counts.head(3).to_dict() if 'Categories' in df.columns and len(categories) > 0 else {},
            'top_keywords': dict(word_counter.most_common(5)) if 'word_counter' in locals() else {}
        }
        
    except Exception as e:
        print(f'❌ 分析失败: {e}')
        return None

def detect_business_type(file_stats):
    """根据分析结果检测业务类型"""
    print(f'\n🔍 业务类型检测')
    print('='*50)
    
    for stats in file_stats:
        if not stats:
            continue
            
        print(f'\n{stats["file_name"]}:')
        
        # 基于关键词和分类判断业务类型
        keywords = stats.get('top_keywords', {})
        categories = stats.get('main_categories', {})
        
        # 分析关键词
        keyword_analysis = []
        for word, count in keywords.items():
            if any(outdoor in word for outdoor in ['outdoor', 'camping', 'tent', 'zelt']):
                keyword_analysis.append('户外用品')
            elif any(home in word for home in ['home', 'haus', 'garten', 'möbel']):
                keyword_analysis.append('家居用品')
            elif any(tool in word for tool in ['werkzeug', 'tool', 'bohrer']):
                keyword_analysis.append('工具设备')
            elif any(fashion in word for fashion in ['kleidung', 'fashion', 'shirt']):
                keyword_analysis.append('服装配饰')
        
        # 分析分类
        category_analysis = []
        for cat, count in categories.items():
            cat_lower = cat.lower()
            if any(outdoor in cat_lower for outdoor in ['outdoor', 'camping', 'sport']):
                category_analysis.append('户外运动')
            elif any(home in cat_lower for home in ['home', 'haus', 'garten', 'möbel']):
                category_analysis.append('家居园艺')
            elif any(tool in cat_lower for tool in ['werkzeug', 'tool', 'technik']):
                category_analysis.append('工具技术')
        
        # 综合判断
        all_analysis = keyword_analysis + category_analysis
        if all_analysis:
            business_type = max(set(all_analysis), key=all_analysis.count)
            print(f'  推测业务类型: {business_type}')
        else:
            print(f'  推测业务类型: 综合零售')
        
        print(f'  主要关键词: {list(keywords.keys())[:3]}')
        print(f'  主要分类: {list(categories.keys())[:2]}')
        print(f'  平均价格: €{stats.get("avg_price", 0):.2f}')

def generate_short_description_strategy(file_stats):
    """生成短描述策略"""
    print(f'\n💡 短描述生成策略')
    print('='*60)
    
    for stats in file_stats:
        if not stats:
            continue
            
        print(f'\n{stats["file_name"]} 短描述策略:')
        print('-'*40)
        
        keywords = stats.get('top_keywords', {})
        categories = stats.get('main_categories', {})
        avg_price = stats.get('avg_price', 0)
        
        # 基于业务类型制定策略
        if any(outdoor in ' '.join(keywords.keys()).lower() for outdoor in ['outdoor', 'camping']):
            print('业务类型: 户外用品')
            print('短描述模板:')
            print('  {产品名称} ✓ 户外专用设计 ✓ 耐用防水材质 ✓ 便携实用功能')
            
        elif any(home in ' '.join(keywords.keys()).lower() for home in ['haus', 'garten', 'möbel']):
            print('业务类型: 家居用品')
            print('短描述模板:')
            print('  {产品名称} ✓ 优质家居设计 ✓ 实用功能齐全 ✓ 持久耐用品质')
            
        elif any(tool in ' '.join(keywords.keys()).lower() for tool in ['werkzeug', 'tool']):
            print('业务类型: 工具设备')
            print('短描述模板:')
            print('  {产品名称} ✓ 专业工具品质 ✓ 精密制造工艺 ✓ 可靠性能保证')
            
        else:
            print('业务类型: 综合零售')
            print('短描述模板:')
            print('  {产品名称} ✓ 优质产品设计 ✓ 实用功能特色 ✓ 可靠品质保证')
        
        # 价格相关的卖点
        if avg_price > 0:
            if avg_price < 50:
                print('价格定位: 经济实用')
                print('价格卖点: ✓ 超值性价比 ✓ 经济实惠选择')
            elif avg_price < 200:
                print('价格定位: 中档品质')
                print('价格卖点: ✓ 品质价格平衡 ✓ 中档优选产品')
            else:
                print('价格定位: 高端品质')
                print('价格卖点: ✓ 高端品质保证 ✓ 专业级产品')

def main():
    """主函数"""
    print('🔍 无短描述文件分析与优化策略')
    print('='*60)
    
    files_to_analyze = [
        ('woocommerce_output_final/woocommerce_obelink-de_final.csv', 'Obelink'),
        ('woocommerce_output_final/woocommerce_bauhaus-at-de-图片前两图_final.csv', 'Bauhaus')
    ]
    
    file_stats = []
    
    # 分析每个文件
    for file_path, file_name in files_to_analyze:
        try:
            stats = analyze_file_without_short_desc(file_path, file_name)
            if stats:
                file_stats.append(stats)
            print('\n' + '-'*60)
        except Exception as e:
            print(f'❌ 无法分析文件 {file_name}: {e}')
    
    # 检测业务类型
    if file_stats:
        detect_business_type(file_stats)
        
        # 生成短描述策略
        generate_short_description_strategy(file_stats)
        
        # 总结建议
        print(f'\n📋 总结建议')
        print('='*60)
        print('1. 这两个文件确实需要生成短描述')
        print('2. 基于产品名称和分类生成定制化短描述')
        print('3. 每个产品3个核心卖点，突出功能和品质')
        print('4. 根据价格定位调整卖点重点')
        print('5. 保持德语表达的自然性和专业性')
        
        print(f'\n下一步行动:')
        print('• 创建短描述生成脚本')
        print('• 基于产品名称和分类智能生成')
        print('• 验证生成质量和相关性')

if __name__ == "__main__":
    main()
