说明与小贴士

多变体：父行 Type=variable，子行 Type=variation；父 SKU = 域名前缀-handle，子 SKU = 域名前缀-handle-variantID（若站点原本有 SKU 也会优先使用）。

价格：compare_at_price > price → 视为促销（Sale=price，Regular=compare）；否则只填 Regular=price。

库存：按你的要求不跟踪库存，In stock?=1，Stock 留空。

属性：Shopify 默认最多 3 个 options，我为 CSV 预留了 5 个属性位（4/5 默认空）。父行属性值为全集（Size | Color 这类），变体行为具体值。

短描述：优先 <meta name="description">；没有则从描述正文做提要。

描述清洗：移除 <script>/<style>/<iframe>、去除包含 shopify/ebay 的外链、移除“Powered by Shopify/Shopify/eBay”等字样，并做 HTML 实体解码。

CSV 切分：保证一个商品（父+所有变体）不会被拆跨文件。

断点续采：checkpoint.json 持续更新；异常后再次运行会从断点继续。

编码：CSV 写入 utf-8-sig，WooCommerce 导入不会出现 \u00xx 乱码问题。

fallback：若 sitemap.xml 无法得到产品链接，自动回退 /collections/all 翻页抓。

如果你还想加入分类映射表（把 product_type/vendor/collections 统一映射到你 Woo 的分类树）、或Rank Math 额外 SEO 字段（如 Meta title/description），我可以在这版上继续叠加。