# Shopify 多站点数据采集工具

这是一个增强版的Shopify数据采集脚本，支持单站点和多站点并发采集，将数据转换为WooCommerce兼容的CSV格式。

## 🚀 新功能特性

### ✨ 主要改进
- **多站点并发采集** - 支持同时采集多个Shopify站点
- **智能错误处理** - 完善的异常处理和重试机制
- **进度显示** - 实时显示采集进度和统计信息
- **配置文件支持** - 支持外部配置文件自定义参数
- **随机User-Agent** - 模拟真实浏览器，降低被封风险
- **断点续采** - 支持中断后继续采集
- **数据验证** - 验证采集数据的完整性
- **改进的SKU生成** - 更可靠的SKU生成算法
- **完整的价格支持** - 支持常规价格和促销价格
- **库存管理** - 正确处理库存数量和状态

### 🔧 技术改进
- 使用会话(Session)复用连接
- 智能重试机制，支持指数退避
- 分级日志记录系统
- 线程安全的并发处理
- 内存优化的大文件处理

## 📦 快速开始

### 方法1: 自动安装依赖
```bash
python install_deps.py
```

### 方法2: 手动安装依赖
```bash
pip install -r requirements.txt
```

### 方法3: 逐个安装
```bash
pip install requests beautifulsoup4 tqdm
```

### 验证安装
```bash
python simple_test.py
```

## 🛠️ 配置文件

### config.json - 全局配置
```json
{
  "proxy": null,                    // 代理设置，null表示不使用代理
  "retry_limit": 3,                 // 重试次数
  "sleep_time": 2,                  // 重试间隔(秒)
  "max_workers": 5,                 // 最大并发数
  "timeout": 15,                    // 请求超时时间(秒)
  "user_agents": [...]              // User-Agent列表
}
```

### sites.json - 站点配置
```json
[
  {
    "name": "站点名称",
    "sitemap": "https://store.myshopify.com/sitemap.xml",
    "enabled": true,
    "description": "站点描述"
  }
]
```

## 🚀 使用方法

### 运行脚本
```bash
python scrawshopify.py
```

### 选择模式
1. **单站点采集** - 手动输入一个sitemap URL进行采集
2. **多站点采集** - 从sites.json读取多个站点并发采集
3. **创建站点配置文件** - 生成示例配置文件

### 多站点采集流程
1. 编辑 `sites.json` 添加要采集的站点
2. 运行脚本选择模式2
3. 设置并发数（建议不超过5）
4. 等待采集完成

## 📁 输出文件

- `output/` - CSV输出目录
  - `domain_1.csv`, `domain_2.csv` - 按站点分割的CSV文件
- `collector.log` - 详细日志文件
- `seen_products.json` - 已采集产品记录（断点续采）

## ⚙️ 高级配置

### 代理设置
在 `config.json` 中设置代理：
```json
{
  "proxy": {
    "http": "**********************:port",
    "https": "**********************:port"
  }
}
```

### 并发控制
- `max_workers`: 控制同时采集的站点数量
- 建议根据网络和目标站点情况调整
- 过高的并发可能导致IP被封

### 错误处理
- 自动重试失败的请求
- 智能识别429错误并增加等待时间
- 详细的错误日志记录

## 📊 输出格式

生成的CSV文件完全兼容WooCommerce导入格式，包含：
- 产品基本信息（名称、描述、SKU等）
- 价格信息（常规价格、促销价格）
- 库存信息（库存状态、数量）
- 变体信息（颜色、尺寸等属性）
- 图片链接
- 分类标签
- SEO信息

## 🔍 故障排除

### 常见问题
1. **连接超时** - 检查网络连接或增加timeout值
2. **被封IP** - 使用代理或降低并发数
3. **数据不完整** - 检查sitemap URL是否正确
4. **内存不足** - 减少max_workers或分批处理

### 日志分析
查看 `collector.log` 了解详细的采集过程和错误信息。

## 📈 性能优化建议

1. **合理设置并发数** - 根据网络带宽和目标站点负载能力
2. **使用代理池** - 避免单IP请求过于频繁
3. **定期清理seen_products.json** - 避免文件过大影响性能
4. **监控日志** - 及时发现和处理异常情况

## 🔒 注意事项

- 请遵守目标网站的robots.txt和使用条款
- 合理控制采集频率，避免对目标服务器造成压力
- 建议在非高峰时段进行大规模采集
- 定期备份采集数据和配置文件
