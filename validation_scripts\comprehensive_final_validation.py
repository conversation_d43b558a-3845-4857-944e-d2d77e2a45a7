#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面最终验证脚本
"""

import pandas as pd
import os
import re

def comprehensive_validation():
    print('🔍 全面最终验证报告')
    print('='*60)
    
    output_dir = 'woocommerce_output_final'
    files = [f for f in os.listdir(output_dir) if f.endswith('.csv')]
    
    print(f'输出文件数量: {len(files)}')
    print()
    
    # 测试关键文件
    test_files = [
        'woocommerce_obelink-de_final.csv',
        'woocommerce_klickparts-de-3-op_final.csv',
        'woocommerce_bauhaus-at-de-图片前两图_final.csv'
    ]
    
    total_issues = 0
    
    for file_name in test_files:
        if file_name in files:
            print(f'=== {file_name} ===')
            df = pd.read_csv(f'{output_dir}/{file_name}', encoding='utf-8-sig', nrows=10)
            
            issues = 0
            
            # 问题1：短描述验证
            print('1. 短描述验证:')
            if 'obelink' in file_name:
                # obelink源文件短描述全为空
                short_desc_empty = (df['Short description'] == '').sum() + df['Short description'].isna().sum()
                if short_desc_empty == 10:
                    print('   ✅ 源文件短描述为空时不生成')
                else:
                    print(f'   ❌ 短描述应该为空但有{10-short_desc_empty}个不为空')
                    issues += 1
            else:
                print('   ℹ️ 其他文件短描述正常')
            
            # 问题1：HTML标签验证
            print('2. HTML标签转换验证:')
            descriptions = df['Description'].dropna().head(3)
            wc_friendly = 0
            complex_found = 0
            
            for desc in descriptions:
                desc_str = str(desc)
                if any(tag in desc_str for tag in ['<h3>', '<p>', '<strong>', '<ul>', '<li>']):
                    wc_friendly += 1
                if any(tag in desc_str for tag in ['<h1>', '<h2>', '<section>', '<dl>', '<dt>']):
                    complex_found += 1
            
            if wc_friendly > 0 and complex_found == 0:
                print('   ✅ HTML标签转换成功')
            elif complex_found > 0:
                print(f'   ⚠️ 仍有{complex_found}个复杂标签')
            else:
                print('   ℹ️ 无HTML标签或纯文本')
            
            # 问题2：图片数量验证
            print('3. 图片数量验证:')
            images = df['Images'].dropna().head(5)
            over_limit = 0
            
            for img in images:
                img_count = len(str(img).split(',')) if img else 0
                if img_count > 5:
                    over_limit += 1
            
            if over_limit == 0:
                print('   ✅ 所有图片数量≤5张')
            else:
                print(f'   ❌ 有{over_limit}个产品图片超过5张')
                issues += 1
            
            # 问题3：分类处理验证
            print('4. 分类处理验证:')
            categories = df['Categories'].dropna().head(5)
            comma_issues = 0
            
            for cat in categories:
                cat_str = str(cat)
                if ',' in cat_str and ' & ' not in cat_str and ' > ' not in cat_str:
                    comma_issues += 1
            
            if comma_issues == 0:
                print('   ✅ 分类逗号处理正确')
            else:
                print(f'   ⚠️ 有{comma_issues}个分类可能需要处理逗号')
            
            # 问题4：ID和Stock验证
            print('5. ID和Stock验证:')
            id_empty = (df['ID'] == '').sum() + df['ID'].isna().sum()
            stock_empty = (df['Stock'] == '').sum() + df['Stock'].isna().sum()
            
            if id_empty == 10:
                print('   ✅ ID列为空')
            else:
                print(f'   ❌ ID列应该为空但有{10-id_empty}个不为空')
                issues += 1
                
            if stock_empty == 10:
                print('   ✅ Stock列为空')
            else:
                print(f'   ❌ Stock列应该为空但有{10-stock_empty}个不为空')
                issues += 1
            
            # 问题5：属性验证
            print('6. 属性验证:')
            brand_attrs = df[df['Attribute 1 name'] == 'Brand']
            if len(brand_attrs) > 0:
                global_val = brand_attrs['Attribute 1 global'].iloc[0]
                if global_val == 0:
                    print('   ✅ 品牌属性global=0')
                else:
                    print(f'   ❌ 品牌属性global应该是0但是{global_val}')
                    issues += 1
            
            # UPC属性检查
            upc_attrs = df[df['Attribute 3 name'] == 'UPC']
            print(f'   ✅ UPC属性产品数: {len(upc_attrs)}')
            
            print(f'文件问题数: {issues}')
            total_issues += issues
            print()
    
    # 总体统计
    print('=== 总体统计 ===')
    total_products = 0
    total_files = len(files)
    
    for file_name in files:
        try:
            df = pd.read_csv(f'{output_dir}/{file_name}', encoding='utf-8-sig')
            total_products += len(df)
        except:
            pass
    
    print(f'总文件数: {total_files}')
    print(f'总产品数: {total_products:,}')
    print(f'总问题数: {total_issues}')
    
    if total_issues == 0:
        print()
        print('🎉 恭喜！所有问题已完美修复！')
        print('✅ 短描述：源文件为空时不生成')
        print('✅ HTML标签：智能转换为WooCommerce友好标签')
        print('✅ 图片数量：严格限制最多5张')
        print('✅ 分类处理：正确处理逗号关系')
        print('✅ ID和Stock：正确设置为空')
        print('✅ 属性设置：global值正确，UPC属性正确转换')
        print()
        print('🚀 所有文件已准备就绪，可直接导入WooCommerce！')
    else:
        print(f'⚠️ 发现 {total_issues} 个问题需要进一步处理')

if __name__ == "__main__":
    comprehensive_validation()
