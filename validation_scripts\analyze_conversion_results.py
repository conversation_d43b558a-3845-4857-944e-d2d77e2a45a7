#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
转换结果分析脚本 - 分析WooCommerce转换结果的质量和完整性
"""

import pandas as pd
import numpy as np
from pathlib import Path
from collections import Counter
import re

class ConversionResultAnalyzer:
    def __init__(self):
        # WooCommerce必需字段
        self.required_fields = [
            'ID', 'Type', 'SKU', 'Name', 'Published', 'Regular price', 'Categories'
        ]
        
        # 重要字段
        self.important_fields = [
            'Short description', 'Description', 'Images', 'Stock', 'In stock?'
        ]
        
        # SEO字段
        self.seo_fields = [
            'Meta: _yoast_wpseo_title', 'Meta: _yoast_wpseo_metadesc', 'Meta: _yoast_wpseo_focuskw'
        ]

    def analyze_woocommerce_file(self, file_path: str):
        """分析WooCommerce文件质量"""
        try:
            print(f"📊 分析WooCommerce文件: {Path(file_path).name}")
            print("="*60)
            
            # 读取数据
            df = pd.read_csv(file_path, encoding='utf-8-sig')
            
            print(f"📈 基本信息:")
            print(f"  产品数量: {len(df):,}")
            print(f"  字段数量: {len(df.columns):,}")
            
            # 检查必需字段
            self.check_required_fields(df)
            
            # 检查数据质量
            self.check_data_quality(df)
            
            # 检查价格信息
            self.check_pricing(df)
            
            # 检查分类信息
            self.check_categories(df)
            
            # 检查SEO信息
            self.check_seo_fields(df)
            
            # 生成质量报告
            quality_score = self.calculate_quality_score(df)
            
            print(f"\n🏆 整体质量评分: {quality_score:.1f}/100")
            
            return {
                'file': file_path,
                'products': len(df),
                'fields': len(df.columns),
                'quality_score': quality_score
            }
            
        except Exception as e:
            print(f"❌ 分析失败: {e}")
            return None

    def check_required_fields(self, df: pd.DataFrame):
        """检查必需字段"""
        print(f"\n✅ 必需字段检查:")
        
        missing_fields = []
        for field in self.required_fields:
            if field not in df.columns:
                missing_fields.append(field)
            else:
                empty_count = df[field].isna().sum() + (df[field] == '').sum()
                fill_rate = (len(df) - empty_count) / len(df) * 100
                status = "✅" if fill_rate >= 95 else "⚠️" if fill_rate >= 80 else "❌"
                print(f"  {status} {field:<20} | 填充率: {fill_rate:>5.1f}%")
        
        if missing_fields:
            print(f"  ❌ 缺失字段: {', '.join(missing_fields)}")

    def check_data_quality(self, df: pd.DataFrame):
        """检查数据质量"""
        print(f"\n📋 数据质量检查:")
        
        # SKU唯一性
        if 'SKU' in df.columns:
            sku_duplicates = df['SKU'].duplicated().sum()
            print(f"  SKU重复: {sku_duplicates} 个 ({sku_duplicates/len(df)*100:.1f}%)")
        
        # 产品名称质量
        if 'Name' in df.columns:
            empty_names = (df['Name'].isna() | (df['Name'] == '')).sum()
            avg_name_length = df['Name'].fillna('').astype(str).str.len().mean()
            print(f"  空产品名称: {empty_names} 个 ({empty_names/len(df)*100:.1f}%)")
            print(f"  平均名称长度: {avg_name_length:.1f} 字符")
        
        # 描述质量
        if 'Description' in df.columns:
            empty_desc = (df['Description'].isna() | (df['Description'] == '')).sum()
            print(f"  空描述: {empty_desc} 个 ({empty_desc/len(df)*100:.1f}%)")
        
        # 图片信息
        if 'Images' in df.columns:
            empty_images = (df['Images'].isna() | (df['Images'] == '')).sum()
            print(f"  无图片: {empty_images} 个 ({empty_images/len(df)*100:.1f}%)")

    def check_pricing(self, df: pd.DataFrame):
        """检查价格信息"""
        print(f"\n💰 价格信息检查:")
        
        if 'Regular price' in df.columns:
            price_col = df['Regular price']
            
            # 空价格
            empty_prices = (price_col.isna() | (price_col == '')).sum()
            print(f"  空价格: {empty_prices} 个 ({empty_prices/len(df)*100:.1f}%)")
            
            # 价格统计
            numeric_prices = pd.to_numeric(price_col, errors='coerce').dropna()
            if len(numeric_prices) > 0:
                print(f"  价格范围: ${numeric_prices.min():.2f} - ${numeric_prices.max():.2f}")
                print(f"  平均价格: ${numeric_prices.mean():.2f}")
                print(f"  中位价格: ${numeric_prices.median():.2f}")
                
                # 异常价格检查
                zero_prices = (numeric_prices == 0).sum()
                high_prices = (numeric_prices > 10000).sum()
                print(f"  零价格: {zero_prices} 个")
                print(f"  高价格(>$10,000): {high_prices} 个")
        
        # 促销价格
        if 'Sale price' in df.columns:
            sale_prices = pd.to_numeric(df['Sale price'], errors='coerce').dropna()
            print(f"  有促销价: {len(sale_prices)} 个 ({len(sale_prices)/len(df)*100:.1f}%)")

    def check_categories(self, df: pd.DataFrame):
        """检查分类信息"""
        print(f"\n🏷️ 分类信息检查:")
        
        if 'Categories' in df.columns:
            categories = df['Categories'].fillna('').astype(str)
            non_empty = categories[categories != '']
            
            print(f"  有分类: {len(non_empty)} 个 ({len(non_empty)/len(df)*100:.1f}%)")
            
            if len(non_empty) > 0:
                category_counts = Counter(non_empty)
                print(f"  唯一分类数: {len(category_counts)}")
                
                print(f"  前5个分类:")
                for i, (cat, count) in enumerate(category_counts.most_common(5), 1):
                    print(f"    {i}. {cat}: {count} 个产品")
                
                # 检查未分类产品
                uncategorized = categories[categories.str.lower().isin(['uncategorized', '', 'general'])].count()
                if uncategorized > 0:
                    print(f"  未分类产品: {uncategorized} 个")

    def check_seo_fields(self, df: pd.DataFrame):
        """检查SEO字段"""
        print(f"\n🔍 SEO字段检查:")
        
        for field in self.seo_fields:
            if field in df.columns:
                empty_count = (df[field].isna() | (df[field] == '')).sum()
                fill_rate = (len(df) - empty_count) / len(df) * 100
                status = "✅" if fill_rate >= 80 else "⚠️" if fill_rate >= 50 else "❌"
                print(f"  {status} {field.split(': ')[1]:<25} | 填充率: {fill_rate:>5.1f}%")

    def calculate_quality_score(self, df: pd.DataFrame):
        """计算质量评分"""
        score = 0
        
        # 必需字段完整性 (40分)
        required_score = 0
        for field in self.required_fields:
            if field in df.columns:
                empty_count = df[field].isna().sum() + (df[field] == '').sum()
                fill_rate = (len(df) - empty_count) / len(df)
                required_score += fill_rate * (40 / len(self.required_fields))
        
        score += required_score
        
        # 数据质量 (30分)
        quality_score = 0
        
        # SKU唯一性 (10分)
        if 'SKU' in df.columns:
            sku_duplicates = df['SKU'].duplicated().sum()
            uniqueness_rate = 1 - (sku_duplicates / len(df))
            quality_score += uniqueness_rate * 10
        
        # 产品名称质量 (10分)
        if 'Name' in df.columns:
            empty_names = (df['Name'].isna() | (df['Name'] == '')).sum()
            name_quality = 1 - (empty_names / len(df))
            quality_score += name_quality * 10
        
        # 价格有效性 (10分)
        if 'Regular price' in df.columns:
            valid_prices = pd.to_numeric(df['Regular price'], errors='coerce').notna().sum()
            price_quality = valid_prices / len(df)
            quality_score += price_quality * 10
        
        score += quality_score
        
        # 内容丰富度 (20分)
        content_score = 0
        
        # 描述完整性 (10分)
        if 'Description' in df.columns:
            non_empty_desc = (df['Description'].fillna('') != '').sum()
            desc_rate = non_empty_desc / len(df)
            content_score += desc_rate * 10
        
        # 图片完整性 (10分)
        if 'Images' in df.columns:
            non_empty_images = (df['Images'].fillna('') != '').sum()
            image_rate = non_empty_images / len(df)
            content_score += image_rate * 10
        
        score += content_score
        
        # SEO优化 (10分)
        seo_score = 0
        seo_fields_present = [field for field in self.seo_fields if field in df.columns]
        
        if seo_fields_present:
            for field in seo_fields_present:
                non_empty = (df[field].fillna('') != '').sum()
                field_rate = non_empty / len(df)
                seo_score += field_rate * (10 / len(seo_fields_present))
        
        score += seo_score
        
        return min(score, 100)

    def compare_before_after(self, original_file: str, converted_file: str):
        """比较转换前后的数据"""
        print(f"🔄 转换前后对比分析")
        print("="*60)
        
        try:
            # 读取原始文件
            if original_file.endswith('.csv'):
                df_original = pd.read_csv(original_file, encoding='utf-8-sig')
            else:
                df_original = pd.read_excel(original_file)
            
            # 读取转换后文件
            df_converted = pd.read_csv(converted_file, encoding='utf-8-sig')
            
            print(f"📊 数据量对比:")
            print(f"  原始文件: {len(df_original):,} 行, {len(df_original.columns):,} 列")
            print(f"  转换文件: {len(df_converted):,} 行, {len(df_converted.columns):,} 列")
            print(f"  数据保留率: {len(df_converted)/len(df_original)*100:.1f}%")
            
            # 分析转换质量
            conversion_analysis = self.analyze_woocommerce_file(converted_file)
            
            return {
                'original_products': len(df_original),
                'converted_products': len(df_converted),
                'retention_rate': len(df_converted)/len(df_original)*100,
                'quality_score': conversion_analysis['quality_score'] if conversion_analysis else 0
            }
            
        except Exception as e:
            print(f"❌ 对比分析失败: {e}")
            return None

    def batch_analyze(self, directory: str):
        """批量分析目录中的WooCommerce文件"""
        dir_path = Path(directory)
        
        if not dir_path.exists():
            print(f"❌ 目录不存在: {directory}")
            return []
        
        wc_files = list(dir_path.glob("*woocommerce*.csv")) + list(dir_path.glob("wc-*.csv"))
        
        if not wc_files:
            print(f"❌ 目录中没有WooCommerce文件")
            return []
        
        print(f"🚀 批量分析WooCommerce文件")
        print(f"目录: {directory}")
        print(f"文件数: {len(wc_files)}")
        
        results = []
        
        for i, file_path in enumerate(wc_files, 1):
            print(f"\n[{i}/{len(wc_files)}] " + "="*50)
            result = self.analyze_woocommerce_file(str(file_path))
            if result:
                results.append(result)
        
        # 生成汇总报告
        if results:
            print(f"\n📊 批量分析汇总报告")
            print("="*60)
            
            total_products = sum(r['products'] for r in results)
            avg_quality = sum(r['quality_score'] for r in results) / len(results)
            
            print(f"总文件数: {len(results)}")
            print(f"总产品数: {total_products:,}")
            print(f"平均质量评分: {avg_quality:.1f}/100")
            
            print(f"\n📋 文件详情:")
            for result in results:
                file_name = Path(result['file']).name
                print(f"  • {file_name:<30} | {result['products']:>6,} 产品 | 质量: {result['quality_score']:>5.1f}/100")
        
        return results

def main():
    """主函数"""
    analyzer = ConversionResultAnalyzer()
    
    print("📊 转换结果分析工具")
    print("="*50)
    
    print("📋 使用方法:")
    print("1. 分析单个文件:")
    print("   analyzer.analyze_woocommerce_file('woocommerce_file.csv')")
    print("2. 转换前后对比:")
    print("   analyzer.compare_before_after('original.csv', 'converted.csv')")
    print("3. 批量分析:")
    print("   analyzer.batch_analyze('directory')")

if __name__ == "__main__":
    main()
