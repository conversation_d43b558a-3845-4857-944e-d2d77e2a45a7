#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Shopify采集器测试脚本
用于测试改进后的采集功能
"""

import json
import os
import sys
from scrawshopify import (
    load_config, 
    fetch_url, 
    parse_sitemap, 
    parse_product_json,
    product_to_csv_rows,
    clean_description
)

def test_config_loading():
    """测试配置加载"""
    print("=== 测试配置加载 ===")
    config = load_config()
    print(f"配置加载成功: {len(config)} 个配置项")
    print(f"重试次数: {config['retry_limit']}")
    print(f"超时时间: {config['timeout']}秒")
    print(f"User-Agent数量: {len(config['user_agents'])}")
    return True

def test_html_cleaning():
    """测试HTML清洗功能"""
    print("\n=== 测试HTML清洗 ===")
    
    test_html = """
    <div class="product-desc">
        <p>这是一个产品描述</p>
        <script>alert('bad script')</script>
        <a href="https://shopify.com">Shopify链接</a>
        <img src="image.jpg" alt="产品图片" class="img-class">
        <strong>重要信息</strong>
    </div>
    """
    
    cleaned = clean_description(test_html)
    print("原始HTML长度:", len(test_html))
    print("清洗后长度:", len(cleaned))
    print("清洗结果:", cleaned[:100] + "..." if len(cleaned) > 100 else cleaned)
    
    # 检查是否移除了script标签
    assert "<script>" not in cleaned, "Script标签未被移除"
    print("✓ Script标签已移除")
    
    # 检查是否保留了允许的标签
    assert "<p>" in cleaned, "P标签被错误移除"
    assert "<strong>" in cleaned, "Strong标签被错误移除"
    print("✓ 允许的标签已保留")
    
    return True

def test_sku_generation():
    """测试SKU生成"""
    print("\n=== 测试SKU生成 ===")
    
    from scrawshopify import generate_unique_sku
    
    test_cases = [
        ("test-product-handle", "12345", "TESTPRODUC"),
        ("", "67890", "PROD67890"),
        ("special-chars!@#$%", "11111", "SPECIALCHA"),
    ]
    
    for handle, product_id, expected_prefix in test_cases:
        sku = generate_unique_sku(handle, product_id)
        print(f"Handle: '{handle}' -> SKU: '{sku}'")
        assert sku.startswith(expected_prefix) or sku == f"PROD{product_id}", f"SKU生成错误: {sku}"
    
    print("✓ SKU生成测试通过")
    return True

def test_product_conversion():
    """测试产品数据转换"""
    print("\n=== 测试产品转换 ===")
    
    # 模拟产品数据
    test_product = {
        "id": "12345",
        "title": "测试产品",
        "handle": "test-product",
        "body_html": "<p>产品描述</p>",
        "meta_description": "短描述",
        "tags": ["tag1", "tag2"],
        "images": [
            {"src": "https://example.com/image1.jpg"},
            {"src": "https://example.com/image2.jpg"}
        ],
        "variants": [
            {
                "id": "v1",
                "sku": "TEST-V1",
                "price": "29.99",
                "option1": "红色",
                "option2": "大号",
                "inventory_quantity": 10
            },
            {
                "id": "v2", 
                "sku": "TEST-V2",
                "price": "39.99",
                "option1": "蓝色",
                "option2": "小号",
                "inventory_quantity": 5
            }
        ]
    }
    
    rows = product_to_csv_rows(test_product)
    print(f"转换结果: {len(rows)} 行数据")
    
    # 检查父产品
    parent_row = rows[0]
    assert parent_row["Type"] == "variable", "父产品类型错误"
    assert parent_row["Name"] == "测试产品", "产品名称错误"
    print("✓ 父产品数据正确")
    
    # 检查变体
    variant_rows = rows[1:]
    assert len(variant_rows) == 2, f"变体数量错误: {len(variant_rows)}"
    assert all(row["Type"] == "variation" for row in variant_rows), "变体类型错误"
    print("✓ 变体数据正确")
    
    # 检查价格
    prices = [row.get("Regular price", "") for row in variant_rows]
    assert "29.99" in prices and "39.99" in prices, "价格数据错误"
    print("✓ 价格数据正确")
    
    return True

def test_url_validation():
    """测试URL验证"""
    print("\n=== 测试URL验证 ===")
    
    test_urls = [
        "https://example.myshopify.com/sitemap.xml",
        "http://test-store.com/sitemap.xml", 
        "invalid-url",
        ""
    ]
    
    valid_count = 0
    for url in test_urls:
        try:
            from urllib.parse import urlparse
            result = urlparse(url)
            if result.scheme and result.netloc:
                valid_count += 1
                print(f"✓ 有效URL: {url}")
            else:
                print(f"✗ 无效URL: {url}")
        except Exception as e:
            print(f"✗ URL解析错误 {url}: {e}")
    
    print(f"有效URL数量: {valid_count}/{len(test_urls)}")
    return True

def run_all_tests():
    """运行所有测试"""
    print("开始运行测试...")
    
    tests = [
        test_config_loading,
        test_html_cleaning, 
        test_sku_generation,
        test_product_conversion,
        test_url_validation
    ]
    
    passed = 0
    failed = 0
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_func.__name__} 通过")
            else:
                failed += 1
                print(f"✗ {test_func.__name__} 失败")
        except Exception as e:
            failed += 1
            print(f"✗ {test_func.__name__} 异常: {e}")
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}")
    print(f"失败: {failed}")
    print(f"总计: {passed + failed}")
    
    if failed == 0:
        print("🎉 所有测试通过！")
        return True
    else:
        print("❌ 部分测试失败，请检查代码")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
