#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
站点测试脚本
测试指定站点的采集功能
"""

import sys
import os
import time
import json
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入主脚本的函数
try:
    from scrawshopify import (
        load_config, parse_sitemap, parse_product_json, 
        product_to_csv_rows, CSVWriter, log, fetch_url
    )
except ImportError as e:
    print(f"导入失败: {e}")
    print("请确保在scrawdata目录下运行此脚本")
    sys.exit(1)

def test_site_with_proxy(sitemap_url, max_products=10):
    """使用代理测试站点"""
    print(f"\n=== 使用代理测试: {sitemap_url} ===")
    
    # 确保启用代理
    config = load_config()
    config['enable_proxy'] = True
    
    # 解析sitemap
    print("解析sitemap...")
    urls = parse_sitemap(sitemap_url)
    
    if not urls:
        print("❌ 未找到产品URL")
        return False
    
    print(f"找到 {len(urls)} 个产品URL")
    
    # 限制测试数量
    test_urls = urls[:max_products]
    print(f"测试前 {len(test_urls)} 个产品")
    
    # 创建CSV写入器
    domain = sitemap_url.split('//')[1].split('/')[0]
    csv_writer = CSVWriter(f"{domain}_proxy_test")
    
    success_count = 0
    failed_count = 0
    
    for i, url in enumerate(test_urls, 1):
        print(f"[{i}/{len(test_urls)}] 采集: {url}")
        
        try:
            # 解析产品
            product = parse_product_json(url)
            
            if product:
                # 转换为CSV行
                rows = product_to_csv_rows(product)
                for row in rows:
                    csv_writer.write_row(row)
                
                print(f"  ✅ 成功: {product.get('title', 'Unknown')}")
                success_count += 1
            else:
                print(f"  ❌ 失败: 无法获取产品数据")
                failed_count += 1
                
        except Exception as e:
            print(f"  ❌ 异常: {e}")
            failed_count += 1
        
        # 延迟避免请求过快
        time.sleep(2)
    
    csv_writer.close()
    
    print(f"\n代理测试结果: 成功 {success_count}, 失败 {failed_count}")
    return success_count > 0

def test_site_without_proxy(sitemap_url, max_products=10):
    """不使用代理测试站点"""
    print(f"\n=== 不使用代理测试: {sitemap_url} ===")
    
    # 确保禁用代理
    config = load_config()
    config['enable_proxy'] = False
    
    # 解析sitemap
    print("解析sitemap...")
    urls = parse_sitemap(sitemap_url)
    
    if not urls:
        print("❌ 未找到产品URL")
        return False
    
    print(f"找到 {len(urls)} 个产品URL")
    
    # 限制测试数量
    test_urls = urls[:max_products]
    print(f"测试前 {len(test_urls)} 个产品")
    
    # 创建CSV写入器
    domain = sitemap_url.split('//')[1].split('/')[0]
    csv_writer = CSVWriter(f"{domain}_direct_test")
    
    success_count = 0
    failed_count = 0
    
    for i, url in enumerate(test_urls, 1):
        print(f"[{i}/{len(test_urls)}] 采集: {url}")
        
        try:
            # 解析产品
            product = parse_product_json(url)
            
            if product:
                # 转换为CSV行
                rows = product_to_csv_rows(product)
                for row in rows:
                    csv_writer.write_row(row)
                
                print(f"  ✅ 成功: {product.get('title', 'Unknown')}")
                success_count += 1
            else:
                print(f"  ❌ 失败: 无法获取产品数据")
                failed_count += 1
                
        except Exception as e:
            print(f"  ❌ 异常: {e}")
            failed_count += 1
        
        # 延迟避免请求过快
        time.sleep(1)
    
    csv_writer.close()
    
    print(f"\n直连测试结果: 成功 {success_count}, 失败 {failed_count}")
    return success_count > 0

def test_sitemap_access(sitemap_url):
    """测试sitemap访问"""
    print(f"测试sitemap访问: {sitemap_url}")
    
    try:
        content = fetch_url(sitemap_url)
        if content:
            print(f"  ✅ sitemap访问成功，内容长度: {len(content)}")
            return True
        else:
            print(f"  ❌ sitemap访问失败")
            return False
    except Exception as e:
        print(f"  ❌ sitemap访问异常: {e}")
        return False

def main():
    """主函数"""
    print("=== Shopify站点测试工具 ===")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试站点
    test_sites = [
        "https://courtsidetennis.com/sitemap.xml",
        "https://www.solartopstore.com/sitemap.xml"
    ]
    
    results = []
    
    for sitemap_url in test_sites:
        print(f"\n{'='*60}")
        print(f"测试站点: {sitemap_url}")
        print(f"{'='*60}")
        
        # 先测试sitemap访问
        if not test_sitemap_access(sitemap_url):
            print("跳过此站点，sitemap无法访问")
            continue
        
        # 测试直连模式
        direct_success = test_site_without_proxy(sitemap_url, 10)
        
        # 测试代理模式
        proxy_success = test_site_with_proxy(sitemap_url, 10)
        
        results.append({
            'site': sitemap_url,
            'direct': direct_success,
            'proxy': proxy_success
        })
    
    # 显示汇总结果
    print(f"\n{'='*60}")
    print("测试汇总结果")
    print(f"{'='*60}")
    
    for result in results:
        site_name = result['site'].split('//')[1].split('/')[0]
        direct_status = "✅ 成功" if result['direct'] else "❌ 失败"
        proxy_status = "✅ 成功" if result['proxy'] else "❌ 失败"
        
        print(f"{site_name}:")
        print(f"  直连模式: {direct_status}")
        print(f"  代理模式: {proxy_status}")
    
    print(f"\n测试完成，请检查output目录下的CSV文件")
    print("文件命名格式: 域名_direct_test_1.csv 和 域名_proxy_test_1.csv")

if __name__ == "__main__":
    main()
