# 🚀 快速开始指南

## 1️⃣ 安装依赖

```bash
# 自动安装（推荐）
python install_deps.py

# 或手动安装
pip install requests beautifulsoup4 tqdm requests[socks]
```

## 2️⃣ 配置SOCKS5代理

### 编辑 proxies.txt
```bash
# 添加你的SOCKS5代理
************:5727:dxgetpjh:nsueipnrildn
*************:6455:dxgetpjh:nsueipnrildn
************:6045:dxgetpjh:nsueipnrildn
*************:6131:dxgetpjh:nsueipnrildn
**********:5688:dxgetpjh:nsueipnrildn
```

### 测试代理
```bash
python test_proxy.py
# 选择选项2测试所有代理
```

## 3️⃣ 配置站点列表

### 编辑 sites.txt (推荐)
```bash
# 简单格式，每行一个站点
我的商店1|https://store1.myshopify.com/sitemap.xml
我的商店2|https://store2.myshopify.com/sitemap.xml
https://store3.myshopify.com/sitemap.xml

# 注释掉不需要的
# 暂停商店|https://paused.myshopify.com/sitemap.xml
```

## 4️⃣ 开始采集

```bash
python scrawshopify.py
```

选择采集模式：
- **模式1**: 单站点采集（手动输入URL）
- **模式2**: 多站点采集（从sites.txt读取）
- **模式3**: 创建配置文件

## 5️⃣ 查看结果

采集完成后检查：
- `output/` 目录 - CSV文件
- `collector.log` - 详细日志
- `seen_products.json` - 已采集记录

## ⚙️ 重要配置说明

### 代理开关 (重要!)
- `enable_proxy`: true/false（代理总开关）
- `proxy_type`: "socks5" 或 "http"
- `use_proxy_rotation`: true（轮换代理）
- `max_workers`: 3（使用代理时建议降低）

### 快速开关代理
```bash
# 方法1: 使用专用工具
python proxy_switch.py

# 方法2: 在主程序中选择
python scrawshopify.py
# 选择选项4 - 代理设置

# 方法3: 直接编辑config.json
# 设置 "enable_proxy": false 禁用代理
# 设置 "enable_proxy": true 启用代理
```

### 性能调优
- 并发数不要太高（建议1-3）
- 适当增加sleep_time避免被封
- 使用高质量住宅IP代理

## 🔧 常见问题

### Q: 如何关闭代理？
A: 三种方法任选一种：
   - 运行 `python proxy_switch.py` 选择禁用
   - 运行 `python scrawshopify.py` 选择选项4
   - 编辑config.json设置 `"enable_proxy": false`

### Q: 代理连接失败？
A: 检查代理格式和有效性，运行 `python test_proxy.py`

### Q: 没有找到产品？
A: 确认sitemap URL正确，检查是否包含/products/路径

### Q: 被网站封禁？
A: 降低并发数，增加延迟时间，更换代理IP

### Q: 想要暂停某个站点？
A: 在sites.txt中用#注释掉对应行

### Q: 直连速度更快？
A: 如果网络环境好，可以关闭代理提高速度

## 📁 文件说明

| 文件 | 用途 |
|------|------|
| `scrawshopify.py` | 主采集脚本 |
| `config.json` | 全局配置 |
| `proxies.txt` | SOCKS5代理列表 |
| `sites.txt` | 站点列表（推荐） |
| `sites.json` | 站点列表（JSON格式） |
| `proxy_switch.py` | 代理开关工具 ⭐新增⭐ |
| `check_status.py` | 状态检查工具 ⭐新增⭐ |
| `test_proxy.py` | 代理测试工具 |
| `install_deps.py` | 依赖安装工具 |

## 💡 最佳实践

1. **分批采集**: 不要一次性采集太多站点
2. **监控日志**: 关注collector.log中的错误信息
3. **定期更新**: 更新代理列表和User-Agent
4. **备份数据**: 定期备份output目录和配置文件
5. **合理延迟**: 避免请求过于频繁被封IP

## 🎯 典型使用场景

### 场景1: 单次采集少量站点
```bash
# 编辑sites.txt添加2-3个站点
# 设置max_workers: 1
# 运行采集
```

### 场景2: 批量采集大量站点
```bash
# 分批编辑sites.txt
# 设置max_workers: 2-3
# 使用代理轮换
# 分多次运行
```

### 场景3: 定期更新采集
```bash
# 保持seen_products.json
# 定期运行脚本
# 只会采集新产品
```

开始你的Shopify数据采集之旅吧！ 🎉
