#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests, csv, os, time, sys, json, re
from bs4 import BeautifulSoup
from urllib.parse import urljoin
from xml.etree import ElementTree as ET

# ======================
# 配置
# ======================
CSV_MAX_ROWS = 50000
OUTPUT_DIR = "output"
LOG_FILE = "collector.log"
SEEN_FILE = "seen_products.json"

# IP 代理示例，可用 Webshare 或住宅 IP
PROXY = None
# PROXY = {
#     "http": "*******************:port",
#     "https": "*******************:port"
# }

RETRY_LIMIT = 3
SLEEP_TIME = 2

# ======================
# HTML 描述清洗函数
# ======================
ALLOWED_TAGS = ['p','br','ul','ol','li','strong','em','b','i','h1','h2','h3','h4','img']

def clean_description(html):
    soup = BeautifulSoup(html, 'html.parser')
    for tag in soup.find_all():
        if tag.name not in ALLOWED_TAGS:
            tag.decompose()
        else:
            if tag.name == 'img':
                src = tag.get('src')
                tag.attrs = {'src': src} if src else {}
            else:
                tag.attrs = {}
    # 去掉指向 Shopify 或 Ebay 的链接
    for a in soup.find_all('a'):
        if 'shopify' in a.get('href','').lower() or 'ebay' in a.get('href','').lower():
            a.unwrap()
    return str(soup)

# ======================
# 日志
# ======================
def log(msg):
    print(msg)
    with open(LOG_FILE, 'a', encoding='utf-8') as f:
        f.write(f"{time.strftime('%Y-%m-%d %H:%M:%S')} {msg}\n")

# ======================
# CSV 输出管理
# ======================
class CSVWriter:
    def __init__(self, domain):
        self.domain = domain.replace('https://','').replace('http://','').replace('/','_')
        self.file_index = 1
        self.row_count = 0
        self.file = None
        self.writer = None
        os.makedirs(OUTPUT_DIR, exist_ok=True)
        self.open_new_file()

    def open_new_file(self):
        if self.file:
            self.file.close()
        filename = f"{OUTPUT_DIR}/{self.domain}_{self.file_index}.csv"
        self.file = open(filename, 'w', newline='', encoding='utf-8')
        self.writer = csv.DictWriter(self.file, fieldnames=self.get_headers())
        self.writer.writeheader()
        self.row_count = 0
        self.file_index += 1

    @staticmethod
    def get_headers():
        return [
            "ID","Type","SKU","Name","Published","Is featured?","Visibility in catalog","Short description",
            "Description","Date sale price starts","Date sale price ends","Tax status","Tax class","In stock?",
            "Stock","Low stock amount","Backorders allowed?","Sold individually?","Weight (kg)","Length (cm)",
            "Width (cm)","Height (cm)","Allow customer reviews?","Purchase note","Sale price","Regular price",
            "Categories","Tags","Shipping class","Images","Download limit","Download expiry days","Parent",
            "Grouped products","Upsells","Cross-sells","External URL","Button text","Position",
            "Attribute 1 name","Attribute 1 value(s)","Attribute 1 visible","Attribute 1 global",
            "Attribute 2 name","Attribute 2 value(s)","Attribute 2 visible","Attribute 2 global",
            "Attribute 3 name","Attribute 3 value(s)","Attribute 3 visible","Attribute 3 global",
            "Attribute 4 name","Attribute 4 value(s)","Attribute 4 visible","Attribute 4 global",
            "Attribute 5 name","Attribute 5 value(s)","Attribute 5 visible","Attribute 5 global",
            "Meta: rank_math_focus_keyword"
        ]

    def write_row(self, row):
        if self.row_count >= CSV_MAX_ROWS:
            self.open_new_file()
        self.writer.writerow(row)
        self.row_count += 1

    def close(self):
        if self.file:
            self.file.close()

# ======================
# 请求函数
# ======================
def fetch_url(url):
    for _ in range(RETRY_LIMIT):
        try:
            resp = requests.get(url, proxies=PROXY, timeout=15)
            if resp.status_code == 200:
                return resp.text
        except Exception as e:
            log(f"Error fetching {url}: {e}")
            time.sleep(SLEEP_TIME)
    return None

# ======================
# 解析 sitemap.xml 获取产品 URL
# ======================
def parse_sitemap(sitemap_url):
    html = fetch_url(sitemap_url)
    if not html:
        return []
    urls = []
    try:
        root = ET.fromstring(html)
        for url in root.findall(".//{http://www.sitemaps.org/schemas/sitemap/0.9}loc"):
            u = url.text
            if '/products/' in u:
                urls.append(u)
    except:
        log("解析 sitemap.xml 失败")
    log(f"Found {len(urls)} product URLs in sitemap")
    return urls

# ======================
# 解析产品 JSON
# ======================
def parse_product_json(url):
    json_url = url.rstrip('/') + '.json'
    text = fetch_url(json_url)
    if not text:
        return None
    try:
        data = json.loads(text)
        return data.get('product', {})
    except:
        log(f"JSON 解析失败: {json_url}")
        return None

# ======================
# 转 WooCommerce CSV 行
# ======================
def product_to_csv_rows(product):
    rows = []
    product_title = product.get('title','')
    product_handle = product.get('handle','')
    product_desc = clean_description(product.get('body_html',''))
    short_desc = product.get('meta_description','') or ''
    sku_prefix = product_handle[:8]
    tags = product.get('tags',[])
    if isinstance(tags, list):
        tags_str = ",".join(tags)
    else:
        tags_str = str(tags)
    images = [img.get('src') for img in product.get('images',[])]
    image_csv = ",".join(images)
    variants = product.get('variants',[])

    if not variants:
        row = {
            "ID": "",
            "Type": "simple",
            "SKU": sku_prefix,
            "Name": product_title,
            "Published": 1,
            "Short description": short_desc,
            "Description": product_desc,
            "Categories": tags_str,
            "Images": image_csv,
            "Meta: rank_math_focus_keyword": product_title,
            "In stock?": "1",
            "Stock": "",
        }
        rows.append(row)
    else:
        parent_row = {
            "ID": "",
            "Type": "variable",
            "SKU": sku_prefix,
            "Name": product_title,
            "Published": 1,
            "Short description": short_desc,
            "Description": product_desc,
            "Categories": tags_str,
            "Images": image_csv,
            "Meta: rank_math_focus_keyword": product_title,
            "In stock?": "1",
            "Stock": "",
        }
        rows.append(parent_row)
        for v in variants:
            variant_sku = v.get('sku') or sku_prefix + str(v.get('id'))
            price = v.get('price','')
            attr1 = v.get('option1','')
            attr2 = v.get('option2','')
            attr3 = v.get('option3','')
            row = {
                "ID": "",
                "Type": "variation",
                "SKU": variant_sku,
                "Name": product_title,
                "Published": 1,
                "Short description": short_desc,
                "Description": product_desc,
                "Categories": tags_str,
                "Images": image_csv,
                "Meta: rank_math_focus_keyword": product_title,
                "In stock?": "1",
                "Stock": "",
                "Regular price": price,
                "Attribute 1 name": "Option 1",
                "Attribute 1 value(s)": attr1,
                "Attribute 1 visible": 1,
                "Attribute 1 global": 1,
                "Attribute 2 name": "Option 2" if attr2 else "",
                "Attribute 2 value(s)": attr2,
                "Attribute 2 visible": 1 if attr2 else 0,
                "Attribute 2 global": 1 if attr2 else 0,
                "Attribute 3 name": "Option 3" if attr3 else "",
                "Attribute 3 value(s)": attr3,
                "Attribute 3 visible": 1 if attr3 else 0,
                "Attribute 3 global": 1 if attr3 else 0,
            }
            rows.append(row)
    return rows

# ======================
# 断点续采管理
# ======================
def load_seen():
    if os.path.exists(SEEN_FILE):
        with open(SEEN_FILE,'r',encoding='utf-8') as f:
            return set(json.load(f))
    return set()

def save_seen(seen):
    with open(SEEN_FILE,'w',encoding='utf-8') as f:
        json.dump(list(seen), f)

# ======================
# 主函数
# ======================
def main():
    sitemap = input("请输入 Shopify sitemap.xml URL: ").strip()
    csv_writer = CSVWriter(sitemap)
    all_urls = parse_sitemap(sitemap)
    if not all_urls:
        log("没有产品 URL，退出。")
        return

    seen_products = load_seen()

    for url in all_urls:
        for attempt in range(RETRY_LIMIT):
            product = parse_product_json(url)
            if product:
                break
            time.sleep(3)
        if not product:
            log(f"跳过产品: {url}")
            continue

        pid = product.get('id')
        if pid in seen_products:
            log(f"已采集，跳过: {product.get('title','')}")
            continue
        seen_products.add(pid)
        save_seen(seen_products)

        rows = product_to_csv_rows(product)
        for r in rows:
            csv_writer.write_row(r)
        log(f"采集完成: {product.get('title','')}")

    csv_writer.close()
    log("全部采集完成！")

if __name__ == '__main__':
    main()
