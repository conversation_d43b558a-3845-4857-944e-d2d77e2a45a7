#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import csv
import os
import time
import sys
import json
import re
import random
import logging
from urllib.parse import urljoin, urlparse
from xml.etree import ElementTree as ET
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime

try:
    from tqdm import tqdm
except ImportError:
    # 如果没有tqdm，使用简单的进度显示
    class tqdm:
        def __init__(self, total=None, desc="", unit=""):
            self.total = total
            self.desc = desc
            self.current = 0
            print(f"{desc}: 开始处理...")

        def update(self, n=1):
            self.current += n
            if self.total:
                percent = (self.current / self.total) * 100
                print(f"{self.desc}: {self.current}/{self.total} ({percent:.1f}%)")

        def __enter__(self):
            return self

        def __exit__(self, *args):
            print(f"{self.desc}: 完成")

try:
    from bs4 import BeautifulSoup
except ImportError:
    print("错误: 缺少 beautifulsoup4 依赖")
    print("请运行: pip install beautifulsoup4")
    sys.exit(1)

# ======================
# 配置
# ======================
CSV_MAX_ROWS = 50000
OUTPUT_DIR = "output"
LOG_FILE = "collector.log"
SEEN_FILE = "seen_products.json"
CONFIG_FILE = "config.json"

# 默认配置
DEFAULT_CONFIG = {
    "proxy": None,
    "retry_limit": 3,
    "sleep_time": 2,
    "max_workers": 5,
    "timeout": 15,
    "user_agents": [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0"
    ]
}

# 全局配置
config = DEFAULT_CONFIG.copy()

# ======================
# 配置管理
# ======================
def load_config():
    """加载配置文件"""
    global config
    if os.path.exists(CONFIG_FILE):
        try:
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                user_config = json.load(f)
                config.update(user_config)
        except Exception as e:
            print(f"配置文件加载失败，使用默认配置: {e}")
    return config

def save_config():
    """保存配置文件"""
    with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)

# ======================
# 日志系统
# ======================
def setup_logging():
    """设置日志系统"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(LOG_FILE, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

logger = setup_logging()

def log(msg, level='info'):
    """统一日志函数"""
    if level == 'error':
        logger.error(msg)
    elif level == 'warning':
        logger.warning(msg)
    else:
        logger.info(msg)

# ======================
# HTML 描述清洗函数
# ======================
ALLOWED_TAGS = ['p','br','ul','ol','li','strong','em','b','i','h1','h2','h3','h4','img']

def clean_description(html):
    """清洗HTML描述"""
    if not html:
        return ""

    try:
        soup = BeautifulSoup(html, 'html.parser')
        for tag in soup.find_all():
            if tag.name not in ALLOWED_TAGS:
                tag.decompose()
            else:
                if tag.name == 'img':
                    src = tag.get('src')
                    tag.attrs = {'src': src} if src else {}
                else:
                    tag.attrs = {}

        # 去掉指向 Shopify 或 Ebay 的链接
        for a in soup.find_all('a'):
            href = a.get('href', '').lower()
            if 'shopify' in href or 'ebay' in href:
                a.unwrap()

        return str(soup)
    except Exception as e:
        log(f"HTML清洗失败: {e}", 'warning')
        return html

# ======================
# CSV 输出管理
# ======================
class CSVWriter:
    def __init__(self, domain):
        self.domain = domain.replace('https://','').replace('http://','').replace('/','_')
        self.file_index = 1
        self.row_count = 0
        self.file = None
        self.writer = None
        os.makedirs(OUTPUT_DIR, exist_ok=True)
        self.open_new_file()

    def open_new_file(self):
        if self.file:
            self.file.close()
        filename = f"{OUTPUT_DIR}/{self.domain}_{self.file_index}.csv"
        self.file = open(filename, 'w', newline='', encoding='utf-8')
        self.writer = csv.DictWriter(self.file, fieldnames=self.get_headers())
        self.writer.writeheader()
        self.row_count = 0
        self.file_index += 1

    @staticmethod
    def get_headers():
        return [
            "ID","Type","SKU","Name","Published","Is featured?","Visibility in catalog","Short description",
            "Description","Date sale price starts","Date sale price ends","Tax status","Tax class","In stock?",
            "Stock","Low stock amount","Backorders allowed?","Sold individually?","Weight (kg)","Length (cm)",
            "Width (cm)","Height (cm)","Allow customer reviews?","Purchase note","Sale price","Regular price",
            "Categories","Tags","Shipping class","Images","Download limit","Download expiry days","Parent",
            "Grouped products","Upsells","Cross-sells","External URL","Button text","Position",
            "Attribute 1 name","Attribute 1 value(s)","Attribute 1 visible","Attribute 1 global",
            "Attribute 2 name","Attribute 2 value(s)","Attribute 2 visible","Attribute 2 global",
            "Attribute 3 name","Attribute 3 value(s)","Attribute 3 visible","Attribute 3 global",
            "Attribute 4 name","Attribute 4 value(s)","Attribute 4 visible","Attribute 4 global",
            "Attribute 5 name","Attribute 5 value(s)","Attribute 5 visible","Attribute 5 global",
            "Meta: rank_math_focus_keyword"
        ]

    def write_row(self, row):
        if self.row_count >= CSV_MAX_ROWS:
            self.open_new_file()
        self.writer.writerow(row)
        self.row_count += 1

    def close(self):
        if self.file:
            self.file.close()

# ======================
# 请求函数
# ======================
def get_random_user_agent():
    """获取随机User-Agent"""
    return random.choice(config['user_agents'])

def fetch_url(url, session=None):
    """改进的URL获取函数"""
    if session is None:
        session = requests.Session()

    headers = {
        'User-Agent': get_random_user_agent(),
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }

    for attempt in range(config['retry_limit']):
        try:
            resp = session.get(
                url,
                proxies=config['proxy'],
                timeout=config['timeout'],
                headers=headers
            )
            if resp.status_code == 200:
                return resp.text
            elif resp.status_code == 429:  # Too Many Requests
                wait_time = config['sleep_time'] * (2 ** attempt)
                log(f"Rate limited, waiting {wait_time}s before retry", 'warning')
                time.sleep(wait_time)
            else:
                log(f"HTTP {resp.status_code} for {url}", 'warning')
        except requests.exceptions.RequestException as e:
            log(f"Request error for {url}: {e}", 'error')
            if attempt < config['retry_limit'] - 1:
                time.sleep(config['sleep_time'] * (attempt + 1))
        except Exception as e:
            log(f"Unexpected error fetching {url}: {e}", 'error')
            break

    return None

# ======================
# 解析 sitemap.xml 获取产品 URL
# ======================
def parse_sitemap(sitemap_url, session=None):
    """解析sitemap获取产品URL"""
    html = fetch_url(sitemap_url, session)
    if not html:
        return []

    urls = []
    try:
        root = ET.fromstring(html)
        # 支持多种sitemap格式
        namespaces = {
            'sitemap': 'http://www.sitemaps.org/schemas/sitemap/0.9'
        }

        # 查找所有URL
        for url_elem in root.findall(".//sitemap:loc", namespaces):
            url = url_elem.text
            if url and ('/products/' in url or '/product/' in url):
                urls.append(url)

        # 如果没找到，尝试不使用命名空间
        if not urls:
            for url_elem in root.findall(".//loc"):
                url = url_elem.text
                if url and ('/products/' in url or '/product/' in url):
                    urls.append(url)

    except ET.ParseError as e:
        log(f"XML解析错误: {e}", 'error')
    except Exception as e:
        log(f"解析sitemap失败: {e}", 'error')

    log(f"从sitemap找到 {len(urls)} 个产品URL")
    return urls

# ======================
# 解析产品 JSON
# ======================
def parse_product_json(url, session=None):
    """解析产品JSON数据"""
    json_url = url.rstrip('/') + '.json'
    text = fetch_url(json_url, session)
    if not text:
        return None

    try:
        data = json.loads(text)
        product = data.get('product', {})

        # 验证产品数据完整性
        if not product.get('title'):
            log(f"产品缺少标题: {json_url}", 'warning')
            return None

        return product
    except json.JSONDecodeError as e:
        log(f"JSON解析失败 {json_url}: {e}", 'error')
        return None
    except Exception as e:
        log(f"解析产品数据失败 {json_url}: {e}", 'error')
        return None

# ======================
# 转 WooCommerce CSV 行
# ======================
def generate_unique_sku(product_handle, product_id):
    """生成唯一SKU"""
    if product_handle:
        base_sku = re.sub(r'[^a-zA-Z0-9]', '', product_handle)[:10]
    else:
        base_sku = f"PROD{product_id}"
    return base_sku.upper()

def product_to_csv_rows(product):
    """转换产品为WooCommerce CSV行"""
    rows = []
    product_title = product.get('title', '')
    product_handle = product.get('handle', '')
    product_id = product.get('id', '')
    product_desc = clean_description(product.get('body_html', ''))
    short_desc = product.get('meta_description', '') or ''

    # 生成更可靠的SKU
    sku_prefix = generate_unique_sku(product_handle, product_id)

    # 处理标签
    tags = product.get('tags', [])
    if isinstance(tags, list):
        tags_str = ",".join(tags)
    else:
        tags_str = str(tags) if tags else ""

    # 处理图片
    images = []
    for img in product.get('images', []):
        src = img.get('src')
        if src:
            images.append(src)
    image_csv = ",".join(images)

    variants = product.get('variants', [])

    # 获取基础价格（用于简单产品或变体产品的默认价格）
    base_price = ""
    if variants:
        # 取第一个变体的价格作为基础价格
        base_price = variants[0].get('price', '')

    if not variants:
        # 简单产品
        row = {
            "ID": "",
            "Type": "simple",
            "SKU": sku_prefix,
            "Name": product_title,
            "Published": 1,
            "Short description": short_desc,
            "Description": product_desc,
            "Categories": tags_str,
            "Images": image_csv,
            "Meta: rank_math_focus_keyword": product_title,
            "In stock?": "1",
            "Stock": "10",  # 默认库存
            "Regular price": base_price,
            "Visibility in catalog": "visible",
            "Allow customer reviews?": 1,
        }
        rows.append(row)
    else:
        # 变体产品 - 父产品
        parent_row = {
            "ID": "",
            "Type": "variable",
            "SKU": sku_prefix,
            "Name": product_title,
            "Published": 1,
            "Short description": short_desc,
            "Description": product_desc,
            "Categories": tags_str,
            "Images": image_csv,
            "Meta: rank_math_focus_keyword": product_title,
            "In stock?": "1",
            "Stock": "",
            "Visibility in catalog": "visible",
            "Allow customer reviews?": 1,
        }
        rows.append(parent_row)

        # 变体产品 - 子产品
        for i, variant in enumerate(variants):
            variant_sku = variant.get('sku') or f"{sku_prefix}-V{i+1}"
            price = variant.get('price', '')
            compare_price = variant.get('compare_at_price', '')

            attr1 = variant.get('option1', '')
            attr2 = variant.get('option2', '')
            attr3 = variant.get('option3', '')

            # 检查库存
            inventory_qty = variant.get('inventory_quantity', 0)
            in_stock = "1" if inventory_qty > 0 else "0"
            stock_qty = str(inventory_qty) if inventory_qty > 0 else ""

            row = {
                "ID": "",
                "Type": "variation",
                "SKU": variant_sku,
                "Name": product_title,
                "Published": 1,
                "Short description": short_desc,
                "Description": product_desc,
                "Categories": tags_str,
                "Images": image_csv,
                "Meta: rank_math_focus_keyword": product_title,
                "In stock?": in_stock,
                "Stock": stock_qty,
                "Regular price": price,
                "Sale price": compare_price if compare_price and float(compare_price) < float(price or 0) else "",
                "Parent": sku_prefix,
                "Attribute 1 name": "Option 1" if attr1 else "",
                "Attribute 1 value(s)": attr1,
                "Attribute 1 visible": 1 if attr1 else 0,
                "Attribute 1 global": 1 if attr1 else 0,
                "Attribute 2 name": "Option 2" if attr2 else "",
                "Attribute 2 value(s)": attr2,
                "Attribute 2 visible": 1 if attr2 else 0,
                "Attribute 2 global": 1 if attr2 else 0,
                "Attribute 3 name": "Option 3" if attr3 else "",
                "Attribute 3 value(s)": attr3,
                "Attribute 3 visible": 1 if attr3 else 0,
                "Attribute 3 global": 1 if attr3 else 0,
            }
            rows.append(row)

    return rows

# ======================
# 断点续采管理
# ======================
def load_seen():
    if os.path.exists(SEEN_FILE):
        with open(SEEN_FILE,'r',encoding='utf-8') as f:
            return set(json.load(f))
    return set()

def save_seen(seen):
    with open(SEEN_FILE,'w',encoding='utf-8') as f:
        json.dump(list(seen), f)

# ======================
# 多站点采集支持
# ======================
def process_single_site(site_info, progress_callback=None):
    """处理单个站点的采集"""
    sitemap_url = site_info['sitemap']
    site_name = site_info.get('name', urlparse(sitemap_url).netloc)

    log(f"开始采集站点: {site_name}")

    # 创建会话
    session = requests.Session()

    # 解析sitemap
    all_urls = parse_sitemap(sitemap_url, session)
    if not all_urls:
        log(f"站点 {site_name} 没有找到产品URL", 'warning')
        return {'site': site_name, 'success': 0, 'failed': 0, 'skipped': 0}

    # 创建CSV写入器
    csv_writer = CSVWriter(sitemap_url)
    seen_products = load_seen()

    stats = {'success': 0, 'failed': 0, 'skipped': 0}

    # 使用进度条
    with tqdm(total=len(all_urls), desc=f"采集 {site_name}", unit="产品") as pbar:
        for i, url in enumerate(all_urls):
            try:
                # 重试机制
                product = None
                for attempt in range(config['retry_limit']):
                    product = parse_product_json(url, session)
                    if product:
                        break
                    if attempt < config['retry_limit'] - 1:
                        time.sleep(config['sleep_time'])

                if not product:
                    log(f"跳过产品 {url}: 无法获取数据", 'warning')
                    stats['failed'] += 1
                    pbar.update(1)
                    continue

                # 检查是否已采集
                pid = str(product.get('id', ''))
                if pid in seen_products:
                    log(f"已采集，跳过: {product.get('title', '')}")
                    stats['skipped'] += 1
                    pbar.update(1)
                    continue

                # 转换并写入CSV
                rows = product_to_csv_rows(product)
                for row in rows:
                    csv_writer.write_row(row)

                # 记录已采集
                seen_products.add(pid)
                save_seen(seen_products)

                stats['success'] += 1
                log(f"采集完成: {product.get('title', '')}")

                # 更新进度
                pbar.update(1)
                if progress_callback:
                    progress_callback(i + 1, len(all_urls), site_name)

                # 随机延迟，避免被封
                time.sleep(random.uniform(1, 3))

            except Exception as e:
                log(f"处理产品失败 {url}: {e}", 'error')
                stats['failed'] += 1
                pbar.update(1)

    csv_writer.close()
    session.close()

    log(f"站点 {site_name} 采集完成: 成功{stats['success']}, 失败{stats['failed']}, 跳过{stats['skipped']}")
    return {'site': site_name, **stats}

def process_multiple_sites(sites, max_workers=None):
    """并发处理多个站点"""
    if max_workers is None:
        max_workers = min(len(sites), config['max_workers'])

    results = []

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_site = {
            executor.submit(process_single_site, site): site
            for site in sites
        }

        # 收集结果
        for future in as_completed(future_to_site):
            site = future_to_site[future]
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                log(f"站点 {site.get('name', 'Unknown')} 处理失败: {e}", 'error')
                results.append({
                    'site': site.get('name', 'Unknown'),
                    'success': 0,
                    'failed': 1,
                    'skipped': 0
                })

    return results

def load_sites_config():
    """加载站点配置"""
    sites_file = "sites.json"
    if os.path.exists(sites_file):
        try:
            with open(sites_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            log(f"加载站点配置失败: {e}", 'error')

    return []

def create_sample_sites_config():
    """创建示例站点配置文件"""
    sample_sites = [
        {
            "name": "示例站点1",
            "sitemap": "https://example1.com/sitemap.xml",
            "enabled": True
        },
        {
            "name": "示例站点2",
            "sitemap": "https://example2.com/sitemap.xml",
            "enabled": True
        }
    ]

    with open("sites.json", 'w', encoding='utf-8') as f:
        json.dump(sample_sites, f, indent=2, ensure_ascii=False)

    print("已创建示例站点配置文件 sites.json，请编辑后重新运行")

# ======================
# 主函数
# ======================
def main():
    # 加载配置
    load_config()

    print("=== Shopify 多站点数据采集工具 ===")
    print("1. 单站点采集")
    print("2. 多站点采集")
    print("3. 创建站点配置文件")

    choice = input("请选择模式 (1-3): ").strip()

    if choice == "1":
        # 单站点模式
        sitemap = input("请输入 Shopify sitemap.xml URL: ").strip()
        if not sitemap:
            print("URL不能为空")
            return

        site_info = {"sitemap": sitemap, "name": urlparse(sitemap).netloc}
        result = process_single_site(site_info)
        print(f"\n采集完成: {result}")

    elif choice == "2":
        # 多站点模式
        sites = load_sites_config()
        if not sites:
            print("未找到站点配置文件，请先创建")
            return

        # 过滤启用的站点
        enabled_sites = [site for site in sites if site.get('enabled', True)]
        if not enabled_sites:
            print("没有启用的站点")
            return

        print(f"找到 {len(enabled_sites)} 个启用的站点")
        max_workers = int(input(f"并发数 (1-{len(enabled_sites)}, 默认{min(3, len(enabled_sites))}): ") or min(3, len(enabled_sites)))

        results = process_multiple_sites(enabled_sites, max_workers)

        # 显示汇总结果
        print("\n=== 采集汇总 ===")
        total_success = sum(r['success'] for r in results)
        total_failed = sum(r['failed'] for r in results)
        total_skipped = sum(r['skipped'] for r in results)

        for result in results:
            print(f"{result['site']}: 成功{result['success']}, 失败{result['failed']}, 跳过{result['skipped']}")

        print(f"\n总计: 成功{total_success}, 失败{total_failed}, 跳过{total_skipped}")

    elif choice == "3":
        # 创建配置文件
        create_sample_sites_config()

    else:
        print("无效选择")

if __name__ == '__main__':
    main()
