#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Collections API测试脚本
直接使用Collections API采集产品，绕过sitemap
"""

import requests
import json
import csv
import os
import time
from datetime import datetime

# Webshare代理配置
WEBSHARE_PROXY = {
    "http": "**************************************************/",
    "https": "**************************************************/"
}

# 测试站点
TEST_SITES = [
    {"name": "courtsidetennis", "base_url": "https://courtsidetennis.com"},
    {"name": "solartopstore", "base_url": "https://www.solartopstore.com"}
]

def get_collections(base_url):
    """获取站点的所有集合"""
    collections_url = f"{base_url}/collections.json"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    try:
        response = requests.get(
            collections_url,
            headers=headers,
            proxies=WEBSHARE_PROXY,
            timeout=15
        )
        
        if response.status_code == 200:
            data = response.json()
            collections = data.get('collections', [])
            print(f"   找到 {len(collections)} 个集合")
            return collections
        else:
            print(f"   Collections API失败: HTTP {response.status_code}")
            return []
            
    except Exception as e:
        print(f"   Collections API异常: {e}")
        return []

def get_products_from_collection(base_url, collection_handle, max_products=10):
    """从集合获取产品"""
    products_url = f"{base_url}/collections/{collection_handle}/products.json"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    try:
        response = requests.get(
            products_url,
            headers=headers,
            proxies=WEBSHARE_PROXY,
            timeout=15
        )
        
        if response.status_code == 200:
            data = response.json()
            products = data.get('products', [])
            
            # 限制产品数量
            if len(products) > max_products:
                products = products[:max_products]
            
            print(f"     集合 '{collection_handle}': {len(products)} 个产品")
            return products
        else:
            print(f"     集合 '{collection_handle}' 失败: HTTP {response.status_code}")
            return []
            
    except Exception as e:
        print(f"     集合 '{collection_handle}' 异常: {e}")
        return []

def convert_product_to_csv_row(product):
    """转换产品为CSV行"""
    title = product.get('title', '')
    handle = product.get('handle', '')
    body_html = product.get('body_html', '')
    
    # 简单清理HTML
    import re
    description = re.sub(r'<[^>]+>', '', body_html) if body_html else ''
    description = description.strip()[:500]  # 限制长度
    
    # 获取价格
    variants = product.get('variants', [])
    price = variants[0].get('price', '') if variants else ''
    
    # 获取图片
    images = product.get('images', [])
    image_urls = [img.get('src', '') for img in images[:3]]  # 最多3张图片
    image_csv = ','.join(image_urls)
    
    # 获取标签
    tags = product.get('tags', [])
    if isinstance(tags, list):
        tags_str = ','.join(tags)
    else:
        tags_str = str(tags) if tags else ''
    
    return {
        'ID': '',
        'Type': 'simple',
        'SKU': handle[:10].upper() if handle else f"PROD{product.get('id', '')}",
        'Name': title,
        'Published': 1,
        'Short description': description[:200],
        'Description': description,
        'Categories': tags_str,
        'Images': image_csv,
        'Regular price': price,
        'In stock?': 1,
        'Stock': 10,
        'Visibility in catalog': 'visible',
        'Allow customer reviews?': 1
    }

def test_site_collections(site_info, max_products_per_collection=5):
    """测试站点的Collections API采集"""
    site_name = site_info['name']
    base_url = site_info['base_url']
    
    print(f"\n=== 测试 {site_name} Collections API ===")
    print(f"基础URL: {base_url}")
    
    # 1. 获取集合列表
    print("1. 获取集合列表...")
    collections = get_collections(base_url)
    
    if not collections:
        print("   ❌ 未找到集合")
        return {'success': 0, 'failed': 1, 'products': []}
    
    # 2. 从前几个集合获取产品
    print("2. 从集合获取产品...")
    all_products = []
    
    # 只测试前3个集合
    test_collections = collections[:3]
    
    for collection in test_collections:
        collection_handle = collection.get('handle', '')
        collection_title = collection.get('title', '')
        
        print(f"   测试集合: {collection_title} ({collection_handle})")
        
        products = get_products_from_collection(
            base_url, 
            collection_handle, 
            max_products_per_collection
        )
        
        all_products.extend(products)
        time.sleep(1)  # 避免请求过快
    
    # 3. 转换产品数据
    print("3. 转换产品数据...")
    csv_rows = []
    
    for product in all_products:
        try:
            row = convert_product_to_csv_row(product)
            csv_rows.append(row)
            print(f"   ✅ {product.get('title', 'Unknown')[:40]}...")
        except Exception as e:
            print(f"   ❌ 产品转换失败: {e}")
    
    result = {
        'success': len(csv_rows),
        'failed': len(all_products) - len(csv_rows),
        'products': csv_rows
    }
    
    print(f"\n{site_name} 测试结果:")
    print(f"   成功: {result['success']}")
    print(f"   失败: {result['failed']}")
    
    return result

def save_results_to_csv(site_name, products):
    """保存结果到CSV"""
    if not products:
        return
    
    os.makedirs('output', exist_ok=True)
    filename = f"output/{site_name}_collections_test.csv"
    
    # 获取所有字段名
    if products:
        fieldnames = products[0].keys()
        
        with open(filename, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(products)
        
        print(f"   结果已保存到: {filename}")

def main():
    """主函数"""
    print("=== Collections API 测试工具 ===")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"使用Webshare代理: {WEBSHARE_PROXY['http']}")
    
    all_results = []
    
    for site in TEST_SITES:
        result = test_site_collections(site, max_products_per_collection=5)
        save_results_to_csv(site['name'], result['products'])
        all_results.append({
            'site': site['name'],
            'result': result
        })
    
    # 显示汇总结果
    print(f"\n{'='*60}")
    print("测试汇总结果")
    print(f"{'='*60}")
    
    total_success = 0
    total_failed = 0
    
    for result in all_results:
        site_name = result['site']
        success = result['result']['success']
        failed = result['result']['failed']
        
        total_success += success
        total_failed += failed
        
        status = "✅" if success > 0 else "❌"
        print(f"{status} {site_name}: 成功 {success}, 失败 {failed}")
    
    print(f"\n总计: 成功 {total_success}, 失败 {total_failed}")
    
    if total_success > 0:
        print("🎉 Collections API测试成功！")
        print("请检查output目录下的CSV文件查看采集结果")
    else:
        print("❌ 测试失败，可能这些站点不支持Collections API")

if __name__ == "__main__":
    main()
