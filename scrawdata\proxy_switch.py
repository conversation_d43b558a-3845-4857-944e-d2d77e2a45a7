#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
代理开关工具
快速启用/禁用代理功能
"""

import json
import os

CONFIG_FILE = "config.json"

def load_config():
    """加载配置"""
    if os.path.exists(CONFIG_FILE):
        try:
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"配置文件加载失败: {e}")
            return {}
    return {}

def save_config(config):
    """保存配置"""
    try:
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        return True
    except Exception as e:
        print(f"配置文件保存失败: {e}")
        return False

def get_proxy_count():
    """获取代理数量"""
    proxy_file = "proxies.txt"
    if not os.path.exists(proxy_file):
        return 0
    
    count = 0
    try:
        with open(proxy_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    count += 1
        return count
    except:
        return 0

def show_status(config):
    """显示当前状态"""
    enable_proxy = config.get('enable_proxy', False)
    proxy_count = get_proxy_count()
    proxy_file = config.get('proxy_file', 'proxies.txt')
    proxy_type = config.get('proxy_type', 'socks5')
    
    print("=== 当前代理状态 ===")
    print(f"代理状态: {'✅ 启用' if enable_proxy else '❌ 禁用'}")
    print(f"代理数量: {proxy_count}")
    print(f"代理文件: {proxy_file}")
    print(f"代理类型: {proxy_type}")
    print(f"最大并发: {config.get('max_workers', 3)}")

def enable_proxy(config):
    """启用代理"""
    config['enable_proxy'] = True
    if save_config(config):
        print("✅ 代理已启用")
        return True
    return False

def disable_proxy(config):
    """禁用代理"""
    config['enable_proxy'] = False
    if save_config(config):
        print("❌ 代理已禁用，将使用直连模式")
        return True
    return False

def main():
    """主函数"""
    print("=== 代理开关工具 ===")
    
    config = load_config()
    if not config:
        print("未找到配置文件，请先运行主程序")
        return
    
    while True:
        print()
        show_status(config)
        
        print("\n选择操作:")
        print("1. 启用代理")
        print("2. 禁用代理")
        print("3. 切换代理状态")
        print("4. 刷新状态")
        print("5. 退出")
        
        choice = input("\n请选择 (1-5): ").strip()
        
        if choice == "1":
            enable_proxy(config)
            
        elif choice == "2":
            disable_proxy(config)
            
        elif choice == "3":
            # 切换状态
            current_status = config.get('enable_proxy', False)
            if current_status:
                disable_proxy(config)
            else:
                enable_proxy(config)
                
        elif choice == "4":
            # 重新加载配置
            config = load_config()
            print("状态已刷新")
            
        elif choice == "5":
            print("退出")
            break
            
        else:
            print("无效选择")

if __name__ == "__main__":
    main()
