#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
状态检查工具
快速查看当前配置状态
"""

import json
import os

def check_config():
    """检查配置状态"""
    print("=== 配置状态检查 ===")
    
    # 检查config.json
    if os.path.exists("config.json"):
        try:
            with open("config.json", 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            print("✅ config.json 存在")
            print(f"   代理状态: {'启用' if config.get('enable_proxy', False) else '禁用'}")
            print(f"   代理类型: {config.get('proxy_type', 'socks5')}")
            print(f"   最大并发: {config.get('max_workers', 3)}")
            print(f"   重试次数: {config.get('retry_limit', 3)}")
            
        except Exception as e:
            print(f"❌ config.json 读取失败: {e}")
    else:
        print("❌ config.json 不存在")

def check_proxies():
    """检查代理文件"""
    print("\n=== 代理文件检查 ===")
    
    if os.path.exists("proxies.txt"):
        try:
            proxy_count = 0
            with open("proxies.txt", 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        proxy_count += 1
            
            print("✅ proxies.txt 存在")
            print(f"   代理数量: {proxy_count}")
            
        except Exception as e:
            print(f"❌ proxies.txt 读取失败: {e}")
    else:
        print("❌ proxies.txt 不存在")

def check_sites():
    """检查站点文件"""
    print("\n=== 站点文件检查 ===")
    
    # 检查sites.txt
    if os.path.exists("sites.txt"):
        try:
            site_count = 0
            with open("sites.txt", 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        site_count += 1
            
            print("✅ sites.txt 存在")
            print(f"   站点数量: {site_count}")
            
        except Exception as e:
            print(f"❌ sites.txt 读取失败: {e}")
    else:
        print("❌ sites.txt 不存在")
    
    # 检查sites.json
    if os.path.exists("sites.json"):
        try:
            with open("sites.json", 'r', encoding='utf-8') as f:
                sites = json.load(f)
            
            enabled_count = sum(1 for site in sites if site.get('enabled', True))
            
            print("✅ sites.json 存在")
            print(f"   总站点数: {len(sites)}")
            print(f"   启用站点: {enabled_count}")
            
        except Exception as e:
            print(f"❌ sites.json 读取失败: {e}")
    else:
        print("❌ sites.json 不存在")

def check_output():
    """检查输出目录"""
    print("\n=== 输出目录检查 ===")
    
    if os.path.exists("output"):
        try:
            files = [f for f in os.listdir("output") if f.endswith('.csv')]
            print("✅ output 目录存在")
            print(f"   CSV文件数: {len(files)}")
            
            if files:
                print("   最近的文件:")
                for f in sorted(files)[-3:]:  # 显示最近3个文件
                    print(f"     - {f}")
                    
        except Exception as e:
            print(f"❌ output 目录读取失败: {e}")
    else:
        print("❌ output 目录不存在")

def check_logs():
    """检查日志文件"""
    print("\n=== 日志文件检查 ===")
    
    if os.path.exists("collector.log"):
        try:
            size = os.path.getsize("collector.log")
            print("✅ collector.log 存在")
            print(f"   文件大小: {size} 字节")
            
        except Exception as e:
            print(f"❌ collector.log 读取失败: {e}")
    else:
        print("❌ collector.log 不存在")
    
    if os.path.exists("seen_products.json"):
        try:
            with open("seen_products.json", 'r', encoding='utf-8') as f:
                seen = json.load(f)
            
            print("✅ seen_products.json 存在")
            print(f"   已采集产品: {len(seen)}")
            
        except Exception as e:
            print(f"❌ seen_products.json 读取失败: {e}")
    else:
        print("❌ seen_products.json 不存在")

def main():
    """主函数"""
    print("=== Shopify采集器状态检查 ===\n")
    
    check_config()
    check_proxies()
    check_sites()
    check_output()
    check_logs()
    
    print("\n=== 建议操作 ===")
    
    # 根据检查结果给出建议
    if not os.path.exists("config.json"):
        print("🔧 运行 python scrawshopify.py 创建配置文件")
    
    if not os.path.exists("proxies.txt"):
        print("🔧 创建 proxies.txt 文件添加代理")
    
    if not os.path.exists("sites.txt") and not os.path.exists("sites.json"):
        print("🔧 运行 python scrawshopify.py 选择选项3创建站点配置")
    
    print("🚀 准备就绪后运行 python scrawshopify.py 开始采集")

if __name__ == "__main__":
    main()
