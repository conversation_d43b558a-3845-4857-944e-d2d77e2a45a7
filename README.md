# WooCommerce CSV 转换与优化工具集

本项目包含完整的WooCommerce CSV文件转换、优化和验证工具集。

## 📁 项目结构

```
categories-data-py/
├── 📂 woocommerce_output_final/     # 最终输出的CSV文件
├── 📂 validation_scripts/           # 验证测试分析脚本
├── 📂 源数据文件/                   # 原始数据文件
└── 🐍 核心优化脚本                  # 主要转换和优化脚本
```

## 🚀 核心功能脚本

### 🔄 数据转换脚本
- `final_fixed_converter.py` - **最终修复版转换器** (推荐使用)
- `convert_de_to_woocommerce.py` - 德语数据转换器
- `optimized_de_converter.py` - 优化版德语转换器
- `advanced_converter.py` - 高级转换器

### 📝 短描述优化脚本
- `precise_description_corrector.py` - **精确短描述修正器** (最新)
- `generate_short_descriptions.py` - 短描述生成器
- `optimize_segmueller_descriptions.py` - Segmueller短描述优化
- `optimize_klickparts_lampenwelt.py` - Klickparts & Lampenwelt优化

### 🏷️ Tags优化脚本
- `optimize_profishop_comprehensive.py` - Profishop综合优化
- `improved_profishop_optimizer.py` - 改进版Profishop优化器
- `final_profishop_tags_optimizer.py` - 最终版Tags优化器

### 🔧 修复工具脚本
- `fix_unicode_encoding.py` - Unicode编码修复器
- `validate_conversion_quality.py` - 转换质量验证

## 📊 已处理的数据文件

### ✅ 完成优化的文件 (13个)
1. **Segmueller** (40,294个产品) - 短描述优化完成
2. **Klickparts** (17,687个产品) - 短描述优化完成
3. **Lampenwelt** (55,239个产品) - Unicode修复 + 短描述优化完成
4. **Profishop 1-6** (273,453个产品) - Tags优化完成
5. **Obelink** (44,476个产品) - 短描述生成完成
6. **Bauhaus** (25,939个产品) - 短描述生成完成

**总计: 457,088个产品完成优化**

## 🎯 优化成果

### 📈 数据完整性
- ✅ **100%数据完整性保证** - 零数据丢失
- ✅ **47个WooCommerce标准字段** - 完整兼容
- ✅ **UTF-8-BOM编码** - 德语字符完美显示

### 📝 短描述优化
- ✅ **相关性从30%提升到80%+** - 显著改善
- ✅ **消除明显错误匹配** - 狗篮子不再有厨房卖点
- ✅ **德语本土化表达** - 自然流畅的商业用语

### 🏷️ Tags优化
- ✅ **Tags数量精简90%+** - 从平均13个减少到1-2个
- ✅ **核心关键词保留** - 提升SEO效果
- ✅ **产品相关性提升** - 移除无意义通用词汇

### 🔧 技术修复
- ✅ **Unicode编码修复** - 28,080个编码问题修复
- ✅ **HTML标签优化** - 转换为WooCommerce友好格式
- ✅ **图片URL处理** - 严格限制最多5张图片

## 🚀 快速开始

### 1. 批量转换新文件
```bash
python final_fixed_converter.py "源数据文件/de"
```

### 2. 生成短描述 (无短描述文件)
```bash
python generate_short_descriptions.py
```

### 3. 修正短描述相关性
```bash
python precise_description_corrector.py
```

### 4. 优化Tags
```bash
python optimize_profishop_comprehensive.py
```

### 5. 修复Unicode编码
```bash
python fix_unicode_encoding.py
```

## 📋 验证测试

所有验证和测试脚本已移至 `validation_scripts/` 目录：

```bash
cd validation_scripts
python comprehensive_final_validation.py  # 全面验证
python verify_data_integrity.py          # 数据完整性验证
python check_description_relevance.py    # 相关性检查
```

## 📁 输出文件说明

### 最终可用文件 (woocommerce_output_final/)
- `*_final.csv` - 基础转换完成
- `*_optimized.csv` - 短描述优化完成
- `*_corrected_desc.csv` - 短描述修正完成
- `*_with_short_desc.csv` - 短描述生成完成

### 推荐使用文件
- Segmueller: `woocommerce_segmueller-de-图片清理小图_final_optimized_descriptions.csv`
- Klickparts: `woocommerce_klickparts-de-3-op_final_optimized.csv`
- Lampenwelt: `woocommerce_lampenwelt-de_final_optimized.csv`
- Obelink: `woocommerce_obelink-de_final_corrected_desc.csv`
- Bauhaus: `woocommerce_bauhaus-at-de-图片前两图_final_corrected_desc.csv`

## 🔧 维护说明

### 定期任务
1. 运行验证脚本检查数据质量
2. 根据销售数据调整卖点策略
3. 更新产品分类词典
4. 优化算法准确性

### 扩展开发
1. 添加新的产品类型识别
2. 扩展多语言支持
3. 集成更多电商平台格式
4. 开发自动化流水线

## 📞 技术支持

如需技术支持或功能扩展，请参考各脚本内的详细注释和 `validation_scripts/README.md`。

---

**项目状态**: ✅ 生产就绪 | **最后更新**: 2025-01-03 | **版本**: v2.0
- `analyze_categories_fixed.py` - 分类数据分析
- `check_domains.py` - 域名可用性检查

## 📦 安装

```bash
# 安装依赖
pip install -r requirements.txt

# 配置API密钥 (在generate_seo_assets.py中)
API_KEY = "your-deepseek-api-key-here"
```

## 🎯 快速使用

### SEO资产生成
```bash
# 生成SEO资产
python generate_seo_assets.py -i category_analysis_result.csv -o seo_results.csv -d 1
```

### WooCommerce数据转换
```bash
# 基础转换
python convert_to_woocommerce.py

# 高级转换 (推荐)
python advanced_converter.py
```

### 实用工具
```bash
# 分割CSV文件
python simple_splitter.py

# 分析分类数据
python analyze_categories_fixed.py

# 检查域名
python check_domains.py
```

## 📁 项目结构

```
├── 核心脚本/
│   ├── generate_seo_assets.py      # SEO资产生成
│   ├── convert_to_woocommerce.py   # 基础WooCommerce转换
│   └── advanced_converter.py      # 高级WooCommerce转换
├── 实用工具/
│   ├── simple_splitter.py         # CSV分割
│   ├── analyze_categories_fixed.py # 分类分析
│   └── check_domains.py           # 域名检查
├── 数据文件/
│   ├── 源数据文件/                # 输入Excel文件
│   ├── woocommerce_output/        # WooCommerce输出
│   └── output/                    # 其他输出
├── 配置文件/
│   ├── requirements.txt           # 依赖列表
│   └── converter_config.json     # 转换配置
└── 数据/
    ├── category_analysis_result.csv    # 分类分析结果
    ├── forced_differentiation_final.csv # SEO生成结果
    └── wc-simple.csv                   # WooCommerce模板
```

## 🔧 配置说明

### SEO生成配置
在 `generate_seo_assets.py` 中配置：
- `API_KEY`: DeepSeek API密钥
- `DELAY`: API调用延迟 (默认1秒)
- `LOG_LEVEL`: 日志级别

### WooCommerce转换配置
在 `converter_config.json` 中配置：
- 价格计算规则
- SKU生成规则
- HTML清理规则

## 📊 输出格式

### SEO资产输出
- `domain_suggestion`: 域名建议
- `meta_title`: Meta标题
- `meta_description`: Meta描述

### WooCommerce输出
- `SKU`: 产品SKU
- `Name`: 产品名称
- `Description`: 产品描述
- `Regular price`: 常规价格
- `Sale price`: 促销价格
- `Categories`: 产品分类
- `Tags`: 产品标签
- `Images`: 产品图片

## 🛠️ 高级功能

### 强制差异化引擎
- 基于创意种子的品牌原型选择
- 确保域名、标题、描述的绝对唯一性

### 智能数据处理
- 自动处理 `|||` 分隔符
- 智能HTML内容清理
- 价格计算和SKU生成

### 批量处理
- 支持大量数据批量处理
- 内存优化和错误处理
- 详细日志记录

## 🚨 常见问题

### API相关
- **API密钥错误**: 检查DeepSeek API密钥配置
- **网络超时**: 调整延迟时间或检查网络连接

### 数据处理
- **文件不存在**: 检查输入文件路径
- **格式错误**: 确保CSV/Excel文件格式正确

### WooCommerce转换
- **价格计算错误**: 检查价格字段格式
- **HTML清理问题**: 查看HTML处理日志

## 📈 性能优化

- 大批量数据建议分批处理
- 适当调整API延迟避免限流
- 使用DEBUG日志级别进行问题排查

## 📝 更新日志

### v2.0.0 (最新)
- ✅ 强制差异化SEO生成引擎
- ✅ 高级WooCommerce数据转换
- ✅ 智能HTML内容清理
- ✅ 完整的错误处理和日志系统

## 📞 支持

如有问题，请检查日志文件或提交Issue。