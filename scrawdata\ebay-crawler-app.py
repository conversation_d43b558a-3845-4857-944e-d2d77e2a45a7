from bs4 import BeautifulSoup
import requests
import csv
import re
import time
import random
import hashlib

# ---------- 配置 ----------
CATEGORY_URL = "https://www.ebay.com/b/Outdoor-Lighting/42154/bn_2311422"
OUTPUT_CSV = "ebay_wc_variations.csv"
FIXED_STOCK = 10
# --------------------------

# ---------- 描述清洗 ----------
def clean_ebay_html(item_specifics_html, description_html):
    def clean_html(html):
        soup = BeautifulSoup(html, "html.parser")
        for tag in soup.find_all(['script', 'style', 'img', 'iframe']):
            tag.decompose()
        for a in soup.find_all('a'):
            a.replace_with(a.get_text())
        for div in soup.find_all('div'):
            p_tag = soup.new_tag('p')
            p_tag.string = div.get_text(separator=' ', strip=True)
            div.replace_with(p_tag)
        text = str(soup)
        text = re.sub(r'\beBay\b', '', text, flags=re.I)
        text = re.sub(r'\n\s*\n', '\n', text)
        return text

    cleaned_specifics = ''
    if item_specifics_html:
        specs_soup = BeautifulSoup(item_specifics_html, "html.parser")
        for table in specs_soup.find_all('table'):
            table['class'] = table.get('class', []) + ['woocommerce-item-specifics']
        cleaned_specifics = clean_html(str(specs_soup))

    cleaned_description = ''
    if description_html:
        cleaned_description = clean_html(description_html)

    final_html = ''
    if cleaned_specifics:
        final_html += cleaned_specifics + '\n'
    if cleaned_description:
        final_html += cleaned_description

    return final_html.strip()

# ---------- 获取页面 ----------
def get_html(url, proxy=None):
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64)"
    }
    if proxy:
        return requests.get(url, headers=headers, proxies=proxy, timeout=15).text
    return requests.get(url, headers=headers, timeout=15).text

# ---------- 生成唯一 SKU ----------
def generate_sku(name, suffix=""):
    # 用产品名 hash 生成唯一 SKU
    hash_str = hashlib.md5(name.encode("utf-8")).hexdigest()[:8].upper()
    sku = "SKU" + hash_str
    if suffix:
        sku += "-" + suffix
    return sku

# ---------- 解析单个产品 ----------
def parse_item_page(url, proxy=None):
    html = get_html(url, proxy)
    soup = BeautifulSoup(html, "html.parser")

    # Title
    title_tag = soup.find("h1", attrs={"id": "itemTitle"})
    name = title_tag.get_text(strip=True).replace("Details about  \xa0", "") if title_tag else "No title"

    # Meta description -> short description
    meta_desc = ''
    meta_tag = soup.find("meta", attrs={"name": "description"})
    if meta_tag:
        meta_desc = meta_tag.get("content", "")
        meta_desc = re.sub(r'\beBay\b', '', meta_desc, flags=re.I)

    # Item specifics
    item_specifics_html = ''
    specifics_div = soup.find("div", attrs={"id": "viTabs_0_is"})
    if specifics_div:
        item_specifics_html = specifics_div.decode_contents()

    # Item description
    description_html = ''
    desc_iframe = soup.find("iframe", attrs={"id": "desc_ifr"})
    if desc_iframe and desc_iframe.has_attr('src'):
        desc_url = desc_iframe['src']
        description_html = get_html(desc_url, proxy)

    description = clean_ebay_html(item_specifics_html, description_html)

    # Images
    images = []
    img_tags = soup.find_all("img", attrs={"id": re.compile("icImg")})
    for img in img_tags:
        if img.has_attr('src'):
            images.append(img['src'])
    images_str = ",".join(images)

    # Categories
    cats = []
    cat_tags = soup.select("ul.breadcrumb li a")
    for c in cat_tags:
        cats.append(c.get_text(strip=True))
    categories = " > ".join(cats)

    # 生成父产品 SKU
    parent_sku = generate_sku(name)

    # 解析变体 (Item specifics 里的选项)
    variations = []
    # 这里只是示例，如果有颜色、尺寸等属性，组合生成变体
    # 实际可以根据实际 Item specifics 做解析
    attributes = {}
    for row in soup.select("div#viTabs_0_is table tr"):
        cols = row.find_all("td")
        if len(cols) >= 2:
            attr_name = cols[0].get_text(strip=True)
            attr_values = cols[1].get_text(strip=True)
            if attr_name and attr_values:
                # 支持多值用逗号分隔
                attr_values_list = [v.strip() for v in attr_values.split(",")]
                attributes[attr_name] = attr_values_list

    # 生成子产品变体组合
    if attributes:
        import itertools
        keys = list(attributes.keys())
        values_lists = [attributes[k] for k in keys]
        for combo in itertools.product(*values_lists):
            var_attrs = dict(zip(keys, combo))
            var_sku = generate_sku(name, "-".join(combo))
            variation = {
                "Type": "variation",
                "SKU": var_sku,
                "Parent": parent_sku,
                "Name": name,
                "Short Description": meta_desc,
                "Description": description,
                "Stock": FIXED_STOCK,
                "Categories": categories,
                "Images": images_str,
                "Attributes": var_attrs,
                "Meta: ebay_sku": var_sku  # 可替换成 eBay 原 SKU，如果抓到的话
            }
            variations.append(variation)
    else:
        # 没有变体，父产品直接作为 simple product
        variations.append({
            "Type": "variable",
            "SKU": parent_sku,
            "Parent": "",
            "Name": name,
            "Short Description": meta_desc,
            "Description": description,
            "Stock": FIXED_STOCK,
            "Categories": categories,
            "Images": images_str,
            "Attributes": {},
            "Meta: ebay_sku": parent_sku
        })

    return variations

# ---------- 获取分类产品列表 ----------
def get_items_from_category(category_url, proxy=None, max_pages=5):
    items = []
    for page in range(1, max_pages + 1):
        paged_url = category_url
        if "?_pgn=" in category_url:
            paged_url = re.sub(r'_pgn=\d+', '_pgn=%d' % page, category_url)
        else:
            paged_url = category_url + ("?_pgn=%d" % page)
        print("抓取列表页:", paged_url)
        html = get_html(paged_url, proxy)
        soup = BeautifulSoup(html, "html.parser")
        links = soup.select("li.s-item a.s-item__link")
        for a in links:
            items.append(a['href'])
        time.sleep(random.uniform(1,2))
    return list(set(items))  # 去重

# ---------- 写入 CSV ----------
def save_to_csv(products):
    keys = ["Type", "SKU", "Parent", "Name", "Short Description", "Description",
            "Stock", "Categories", "Images", "Meta: ebay_sku"]
    # 添加所有可能的属性列
    attr_set = set()
    for p in products:
        if "Attributes" in p:
            for k in p["Attributes"].keys():
                attr_set.add(k)
    keys += sorted(attr_set)
    with open(OUTPUT_CSV, "w", newline="", encoding="utf-8") as f:
        writer = csv.DictWriter(f, fieldnames=keys)
        writer.writeheader()
        for p in products:
            row = {k: p.get(k, "") for k in keys}
            # 填充属性列
            for attr in attr_set:
                row[attr] = p.get("Attributes", {}).get(attr, "")
            writer.writerow(row)

# ---------- 主程序 ----------
def main():
    proxy = {
        "http": "**************************************************/",
        "https": "**************************************************/"
    }

    item_links = get_items_from_category(CATEGORY_URL, proxy)
    print("共抓取到 %d 个产品链接" % len(item_links))

    all_variations = []
    for link in item_links:
        try:
            variations = parse_item_page(link, proxy)
            all_variations.extend(variations)
            print("抓取:", variations[0]["Name"])
            time.sleep(random.uniform(1,2))
        except Exception as e:
            print("抓取失败:", link, e)

    save_to_csv(all_variations)
    print("完成，已生成:", OUTPUT_CSV)

if __name__ == "__main__":
    main()
