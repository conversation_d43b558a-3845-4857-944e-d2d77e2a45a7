#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import time

def test_basic_connection():
    """测试基本网络连接"""
    print("=== 基本网络连接测试 ===")
    
    try:
        print("测试httpbin.org...")
        response = requests.get("http://httpbin.org/ip", timeout=10)
        print(f"✅ httpbin.org 连接成功: {response.status_code}")
        print(f"   响应: {response.text[:100]}")
    except Exception as e:
        print(f"❌ httpbin.org 连接失败: {e}")
    
    try:
        print("\n测试Google...")
        response = requests.get("https://www.google.com", timeout=10)
        print(f"✅ Google 连接成功: {response.status_code}")
    except Exception as e:
        print(f"❌ Google 连接失败: {e}")

def test_sitemap_access():
    """测试sitemap访问"""
    print("\n=== Sitemap访问测试 ===")
    
    sitemaps = [
        "https://courtsidetennis.com/sitemap.xml",
        "https://www.solartopstore.com/sitemap.xml"
    ]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    for sitemap in sitemaps:
        print(f"\n测试: {sitemap}")
        try:
            response = requests.get(sitemap, headers=headers, timeout=15)
            print(f"  状态码: {response.status_code}")
            
            if response.status_code == 200:
                content = response.text
                print(f"  内容长度: {len(content)}")
                print(f"  内容预览: {content[:200]}...")
                
                # 检查是否包含产品URL
                if '/products/' in content:
                    print("  ✅ 包含产品URL")
                else:
                    print("  ❌ 未找到产品URL")
            else:
                print(f"  ❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ 请求失败: {e}")
        
        time.sleep(2)

def main():
    print("=== 快速网络测试 ===")
    
    test_basic_connection()
    test_sitemap_access()
    
    print("\n测试完成!")

if __name__ == "__main__":
    main()
